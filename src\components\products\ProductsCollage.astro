---
import ProductCategoryCard from './ProductCategoryCard.astro';
import categories from '../../../data/product/categories.json';

const categorySlugs = {
  'Плитка тротуарная': 'trotuarnaya-plitka',
  'Брусчатка': 'bruschatka',
  'Бордюры': 'bordyury',
  'Водостоки': 'vodostoki',
  'Ступени': 'stupeni',
  'Элементы декора': 'malye-arhitekturnye-formy'
};
---

<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-gray-900 uppercase mb-2 tracking-wide">Наша продукция</h2>
      <div class="mx-auto w-20 h-1 bg-[#c8b499] mb-2"></div>
    </div>
    <div
      class="grid gap-6"
      style="
        display: grid;
        grid-template-areas:
          'a b c'
          'a b d'
          'e f f';
        grid-template-columns: 1fr 1fr 0.7fr;
        grid-template-rows: 220px 220px 180px;
      "
    >
      {/* 1 — Большой квадрат (row-span 2, col 1) */}
      <div style="grid-area: a; min-height:440px;" class="">
        <ProductCategoryCard image="/images/products/plitka.jpg" title="Плитка тротуарная" description="Разнообразие форм и цветов для любых задач" link={`/products/${categorySlugs['Плитка тротуарная']}`} />
      </div>
      {/* 2 — Большой квадрат (row-span 2, col 2) */}
      <div style="grid-area: b; min-height:440px;" class="">
        <ProductCategoryCard image="/images/products/bruschatka.jpg" title="Брусчатка" description="Классика и современные решения" link={`/products/${categorySlugs['Брусчатка']}`} />
      </div>
      {/* 3 — Малый квадрат (верхний правый) */}
      <div style="grid-area: c; min-height:220px;" class="">
        <ProductCategoryCard image="/images/products/bordyur.jpg" title="Бордюры" description="Надежные и долговечные" link={`/products/${categorySlugs['Бордюры']}`} />
      </div>
      {/* 4 — Малый квадрат (нижний правый) */}
      <div style="grid-area: d; min-height:220px;" class="">
        <ProductCategoryCard image="/images/products/vodostok.jpg" title="Водостоки" description="Эффективное водоотведение" link={`/products/${categorySlugs['Водостоки']}`} />
      </div>
      {/* 5 — Широкий прямоугольник (col-span 1, row 3, col 1) */}
      <div style="grid-area: e; min-height:180px;" class="">
        <ProductCategoryCard image="/images/products/decor.jpg" title="Элементы декора" description="Для ландшафтного дизайна" link={`/products/${categorySlugs['Элементы декора']}`} />
      </div>
      {/* 6 — Широкий прямоугольник (col-span 2, row 3, col 2-3) */}
      <div style="grid-area: f; min-height:180px;" class="">
        <ProductCategoryCard image="/images/products/stupeni.jpg" title="Ступени" description="Прочные и эстетичные решения" link={`/products/${categorySlugs['Ступени']}`} />
      </div>
    </div>
    <div class="flex justify-center mt-12">
      <a href="/products" class="px-10 py-4 bg-[#c8b499] text-white text-lg font-bold uppercase tracking-wider shadow-lg hover:bg-[#b19777] transition-colors">Посмотреть всю продукцию</a>
    </div>
  </div>
  <style>
    @media (max-width: 1024px) {
      .container > .grid {
        display: flex !important;
        flex-direction: column;
      }
      .container > .grid > div {
        min-height: 180px !important;
      }
    }
    @media (max-width: 640px) {
      .container > .grid > div {
        min-height: 120px !important;
      }
    }
  </style>
</section> 