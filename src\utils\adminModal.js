/**
 * Утилиты для модальных окон в админ-панели
 * Предоставляет удобные функции для показа подтверждений и уведомлений
 */

/**
 * Показывает модальное окно подтверждения
 * @param {Object} options - Опции для модального окна
 * @returns {Promise<boolean>} - true если подтверждено, false если отменено
 */
export async function showConfirm(options = {}) {
  if (window.confirmModal) {
    return await window.confirmModal.show(options);
  } else {
    // Fallback к стандартному confirm
    return confirm(options.message || 'Вы уверены?');
  }
}

/**
 * Подтверждение удаления элемента
 * @param {string} itemName - Название удаляемого элемента
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmDelete(itemName = 'этот элемент', options = {}) {
  return showConfirm({
    title: 'Подтверждение удаления',
    message: options.message || `Вы уверены, что хотите удалить ${itemName}? Это действие нельзя отменить.`,
    confirmText: 'Удалить',
    cancelText: 'Отмена',
    confirmButtonClass: 'bg-red-600 hover:bg-red-700 text-white',
    ...options
  });
}

/**
 * Показывает уведомление об успехе
 * @param {string} message - Сообщение
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function showSuccess(message, options = {}) {
  return showConfirm({
    title: 'Успех',
    message,
    confirmText: 'ОК',
    cancelText: '',
    confirmButtonClass: 'bg-green-600 hover:bg-green-700 text-white',
    ...options
  });
}

/**
 * Показывает уведомление об ошибке
 * @param {string} message - Сообщение об ошибке
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function showError(message, options = {}) {
  return showConfirm({
    title: 'Ошибка',
    message,
    confirmText: 'ОК',
    cancelText: '',
    confirmButtonClass: 'bg-red-600 hover:bg-red-700 text-white',
    ...options
  });
}

/**
 * Показывает предупреждение
 * @param {string} message - Сообщение предупреждения
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function showWarning(message, options = {}) {
  return showConfirm({
    title: 'Внимание',
    message,
    confirmText: 'Продолжить',
    cancelText: 'Отмена',
    confirmButtonClass: 'bg-yellow-600 hover:bg-yellow-700 text-white',
    ...options
  });
}

/**
 * Подтверждение сохранения изменений
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmSave(options = {}) {
  return showConfirm({
    title: 'Сохранение изменений',
    message: 'Сохранить внесенные изменения?',
    confirmText: 'Сохранить',
    cancelText: 'Отмена',
    confirmButtonClass: 'bg-green-600 hover:bg-green-700 text-white',
    ...options
  });
}

/**
 * Предупреждение о потере данных при уходе со страницы
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmLeave(options = {}) {
  return showConfirm({
    title: 'Несохраненные изменения',
    message: 'У вас есть несохраненные изменения. Вы уверены, что хотите покинуть страницу?',
    confirmText: 'Покинуть',
    cancelText: 'Остаться',
    confirmButtonClass: 'bg-yellow-600 hover:bg-yellow-700 text-white',
    ...options
  });
}

/**
 * Глобальные функции для использования в inline скриптах
 * Эти функции будут доступны как window.adminModal.*
 */
if (typeof window !== 'undefined') {
  window.adminModal = {
    showConfirm,
    confirmDelete,
    showSuccess,
    showError,
    showWarning,
    confirmSave,
    confirmLeave
  };
}
