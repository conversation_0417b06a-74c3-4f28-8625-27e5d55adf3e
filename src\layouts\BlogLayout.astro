---
import PageLayout from './PageLayout.astro';
import BlogSidebar from '../components/blog/BlogSidebar.astro';

interface Props {
  title: string;
  description?: string;
  pageTitle?: string;
  showBreadcrumbs?: boolean;
  breadcrumbs?: Array<{ text: string; url: string }>;
  showSidebar?: boolean;
}

const {
  title,
  description,
  pageTitle,
  showBreadcrumbs = true,
  breadcrumbs = [],
  showSidebar = true
} = Astro.props;
---

<PageLayout
  title={title}
  pageTitle={pageTitle}
  description={description}
  showBreadcrumbs={showBreadcrumbs}
  breadcrumbs={breadcrumbs}
>
  <div class="container mx-auto px-4 py-12">
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-12">
      <div class="lg:col-span-8">
        <slot />
      </div>
      
      {showSidebar && (
        <aside class="lg:col-span-4">
          <BlogSidebar />
        </aside>
      )}
    </div>
  </div>
</PageLayout> 