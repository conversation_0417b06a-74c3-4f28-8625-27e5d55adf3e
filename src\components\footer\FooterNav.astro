---
const navItems = [
  { text: "Производство", url: "#" },
  { text: "Продукция", url: "#" },
  { text: "Услуги", url: "/services" },
  { text: "Контакты", url: "/contact" },
  { text: "Блог", url: "/blog" },
];
---

<div class="flex flex-col">
   <h2 class="text-white text-lg font-semibold mb-4 uppercase relative pb-2">
    Навигация по сайту
    <span class="absolute bottom-0 left-0 w-5 h-0.5 bg-primary"></span>
  </h2>
  <ul class="text-text-light text-base font-barlow leading-loose">
    {navItems.map(item => (
      <li>
        <a href={item.url} class="hover:text-primary transition-colors duration-200">
           {item.text}
        </a>
      </li>
    ))}
  </ul>
</div> 