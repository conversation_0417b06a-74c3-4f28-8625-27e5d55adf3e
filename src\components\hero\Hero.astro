---
interface Props {
  background: string; // путь к картинке
  title: string;
  subtitle?: string;
}
const { background, title, subtitle } = Astro.props;
---

<section class="relative w-full min-h-[50vh] max-h-[50vh] flex items-center overflow-hidden">
  <img
    src={background}
    alt="Hero background"
    class="absolute inset-0 w-full h-full object-cover object-center z-0"
    loading="eager"
    decoding="async"
    draggable="false"
  />
  <div class="absolute inset-0 bg-black/10 z-10"></div>
  <div class="container relative z-20 flex flex-col items-start justify-center text-left">
    <h1 class="font-sans font-bold text-[2rem] md:text-5xl lg:text-6xl leading-tight mb-4 uppercase text-[#403e39]" style="text-shadow:0 2px 8px rgba(0,0,0,0.10)">{title}</h1>
    {subtitle && (
      <p class="font-barlow text-base md:text-xl text-[#403e39] mb-10">{subtitle}</p>
    )}
    <div class="flex flex-row gap-4">
      <a
        href="/request"
        class="inline-block bg-[#e98135] text-[#403e39] font-bold text-base md:text-lg px-8 py-3 border-2 border-[#c8b499] uppercase tracking-wider transition-colors duration-200 hover:bg-[#b39f86] hover:text-white focus:bg-[#b39f86] focus:text-white focus:outline-none"
        style="border-radius:0"
      >
        Оставить заявку
      </a>
      <a
        href="/products"
        class="inline-block bg-white text-[#403e39] font-bold text-base md:text-lg px-8 py-3 border-2 border-[#c8b499] uppercase tracking-wider transition-colors duration-200 hover:bg-[#c8b499] hover:text-white focus:bg-[#c8b499] focus:text-white focus:outline-none"
        style="border-radius:0"
      >
        Наша продукция
      </a>
    </div>
  </div>
</section> 