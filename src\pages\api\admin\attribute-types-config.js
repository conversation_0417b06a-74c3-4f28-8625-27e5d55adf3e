import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const configPath = path.join(__dirname, '../../../../data/product/attribute-types-config.json');
const attributesPath = path.join(__dirname, '../../../../data/product/attributes.json');

export async function GET() {
  try {
    const data = await fs.readFile(configPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения конфигурации типов атрибутов' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { typeKey, config } = body;

    if (!typeKey || !config) {
      return new Response(JSON.stringify({ error: 'Отсутствуют обязательные данные' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация конфигурации
    const validationError = validateTypeConfig(config);
    if (validationError) {
      return new Response(JSON.stringify({ error: validationError }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Загружаем текущую конфигурацию
    const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));
    
    // Добавляем новый тип
    configData[typeKey] = config;

    // Сохраняем конфигурацию
    await fs.writeFile(configPath, JSON.stringify(configData, null, 2), 'utf-8');

    // Создаем пустой массив для нового типа в основном файле атрибутов
    const attributesData = JSON.parse(await fs.readFile(attributesPath, 'utf-8'));
    if (!attributesData[typeKey]) {
      attributesData[typeKey] = config.isGrouped ? {} : [];
      await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка создания типа атрибута:', error);
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { typeKey, config, oldTypeKey } = body;

    if (!typeKey || !config) {
      return new Response(JSON.stringify({ error: 'Отсутствуют обязательные данные' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация конфигурации
    const validationError = validateTypeConfig(config);
    if (validationError) {
      return new Response(JSON.stringify({ error: validationError }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Загружаем текущую конфигурацию
    const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));
    
    // Если ключ изменился, переносим данные
    if (oldTypeKey && oldTypeKey !== typeKey) {
      if (configData[typeKey] && typeKey !== oldTypeKey) {
        return new Response(JSON.stringify({ error: 'Тип атрибута с таким ключом уже существует' }), {
          status: 409,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Переносим конфигурацию
      configData[typeKey] = config;
      delete configData[oldTypeKey];

      // Переносим данные в основном файле атрибутов
      const attributesData = JSON.parse(await fs.readFile(attributesPath, 'utf-8'));
      if (attributesData[oldTypeKey]) {
        attributesData[typeKey] = attributesData[oldTypeKey];
        delete attributesData[oldTypeKey];
        await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');
      }
    } else {
      // Просто обновляем конфигурацию
      configData[typeKey] = config;
    }

    // Сохраняем конфигурацию
    await fs.writeFile(configPath, JSON.stringify(configData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка обновления типа атрибута:', error);
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { typeKey } = body;

    if (!typeKey) {
      return new Response(JSON.stringify({ error: 'Отсутствует ключ типа атрибута' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Загружаем текущую конфигурацию
    const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));
    
    if (!configData[typeKey]) {
      return new Response(JSON.stringify({ error: 'Тип атрибута не найден' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Удаляем из конфигурации
    delete configData[typeKey];

    // Удаляем из основного файла атрибутов
    const attributesData = JSON.parse(await fs.readFile(attributesPath, 'utf-8'));
    if (attributesData[typeKey]) {
      delete attributesData[typeKey];
      await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');
    }

    // Сохраняем конфигурацию
    await fs.writeFile(configPath, JSON.stringify(configData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка удаления типа атрибута:', error);
    return new Response(JSON.stringify({ error: 'Ошибка удаления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Функция валидации конфигурации типа атрибута
function validateTypeConfig(config) {
  if (!config.name || typeof config.name !== 'string') {
    return 'Название типа атрибута обязательно';
  }

  if (!config.fields || !Array.isArray(config.fields) || config.fields.length === 0) {
    return 'Должно быть определено хотя бы одно поле';
  }

  // Проверяем каждое поле
  for (const field of config.fields) {
    if (!field.key || typeof field.key !== 'string') {
      return 'Каждое поле должно иметь ключ';
    }

    if (!field.name || typeof field.name !== 'string') {
      return 'Каждое поле должно иметь название';
    }

    if (!field.type || typeof field.type !== 'string') {
      return 'Каждое поле должно иметь тип';
    }

    const allowedTypes = ['string', 'number', 'text', 'color', 'boolean', 'select'];
    if (!allowedTypes.includes(field.type)) {
      return `Неподдерживаемый тип поля: ${field.type}`;
    }
  }

  return null; // Валидация прошла успешно
}
