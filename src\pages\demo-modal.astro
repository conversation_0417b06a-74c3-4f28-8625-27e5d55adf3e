---
import PageLayout from '../layouts/PageLayout.astro';
import ConfirmModal from '../components/ui/ConfirmModal.astro';
---

<PageLayout title="Демо модального окна">
  <section class="py-16">
    <div class="container mx-auto px-4 max-w-4xl">
      <h1 class="text-3xl font-bold text-center mb-8">Демо модального окна подтверждения</h1>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Базовые примеры -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h2 class="text-xl font-semibold mb-4">Базовые примеры</h2>
          <div class="space-y-3">
            <button
              id="basic-confirm"
              class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Базовое подтверждение
            </button>

            <button
              id="delete-confirm"
              class="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Подтверждение удаления
            </button>

            <button
              id="warning-confirm"
              class="w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
            >
              Предупреждение
            </button>

            <button
              id="success-confirm"
              class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Успешное действие
            </button>
          </div>
        </div>

        <!-- Кастомные примеры -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h2 class="text-xl font-semibold mb-4">Кастомные примеры</h2>
          <div class="space-y-3">
            <button
              id="custom-text"
              class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
            >
              Кастомный текст
            </button>

            <button
              id="long-message"
              class="w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
            >
              Длинное сообщение
            </button>

            <button
              id="no-cancel"
              class="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Только подтверждение
            </button>

            <button
              id="mobile-test"
              class="w-full px-4 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 transition-colors"
            >
              Тест на мобильном
            </button>
          </div>
        </div>
      </div>

      <!-- Результат -->
      <div class="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 class="text-lg font-semibold mb-2">Результат последнего действия:</h3>
        <p id="result" class="text-gray-600">Нажмите на любую кнопку выше</p>
      </div>

      <!-- Инструкции -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 class="text-lg font-semibold mb-2 text-blue-800">Инструкции для тестирования:</h3>
        <ul class="text-blue-700 space-y-1">
          <li>• Попробуйте разные типы модальных окон</li>
          <li>• Протестируйте на мобильном устройстве (уменьшите окно браузера)</li>
          <li>• Попробуйте закрыть модальное окно разными способами:</li>
          <li>&nbsp;&nbsp;- Кнопка "Отмена"</li>
          <li>&nbsp;&nbsp;- Кнопка "Подтвердить"</li>
          <li>&nbsp;&nbsp;- Клик по фону</li>
          <li>&nbsp;&nbsp;- Клавиша Escape</li>
        </ul>
      </div>
    </div>
  </section>

  <!-- Модальное окно -->
  <ConfirmModal />
</PageLayout>

<script>
  // Функция для показа результата
  function showResult(action, confirmed) {
    const resultElement = document.getElementById('result');
    const timestamp = new Date().toLocaleTimeString();
    resultElement.textContent = `[${timestamp}] ${action}: ${confirmed ? 'Подтверждено' : 'Отменено'}`;
    resultElement.className = confirmed ? 'text-green-600' : 'text-red-600';
  }

  // Ждем загрузки DOM
  document.addEventListener('DOMContentLoaded', () => {
    // Базовое подтверждение
    document.getElementById('basic-confirm').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Подтверждение действия',
        message: 'Вы уверены, что хотите выполнить это действие?',
        confirmText: 'Да',
        cancelText: 'Нет'
      });
      showResult('Базовое подтверждение', confirmed);
    });

    // Подтверждение удаления
    document.getElementById('delete-confirm').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Подтверждение удаления',
        message: 'Вы уверены, что хотите удалить этот элемент? Это действие нельзя отменить.',
        confirmText: 'Удалить',
        cancelText: 'Отмена',
        confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
      });
      showResult('Подтверждение удаления', confirmed);
    });

    // Предупреждение
    document.getElementById('warning-confirm').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Внимание!',
        message: 'Это действие может повлиять на работу системы. Продолжить?',
        confirmText: 'Продолжить',
        cancelText: 'Отмена',
        confirmButtonClass: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border border-yellow-300'
      });
      showResult('Предупреждение', confirmed);
    });

    // Успешное действие
    document.getElementById('success-confirm').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Сохранение изменений',
        message: 'Сохранить внесенные изменения?',
        confirmText: 'Сохранить',
        cancelText: 'Отмена',
        confirmButtonClass: 'bg-green-100 hover:bg-green-200 text-green-700 border border-green-300'
      });
      showResult('Сохранение изменений', confirmed);
    });

    // Кастомный текст
    document.getElementById('custom-text').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Кастомное модальное окно',
        message: 'Это пример модального окна с полностью кастомным текстом и кнопками.',
        confirmText: 'Круто!',
        cancelText: 'Не очень',
        confirmButtonClass: 'bg-purple-600 hover:bg-purple-700 text-white'
      });
      showResult('Кастомный текст', confirmed);
    });

    // Длинное сообщение
    document.getElementById('long-message').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Очень важное уведомление',
        message: 'Это очень длинное сообщение, которое демонстрирует, как модальное окно обрабатывает большие объемы текста. Модальное окно должно автоматически адаптироваться к размеру контента и корректно отображаться на всех устройствах, включая мобильные телефоны и планшеты.',
        confirmText: 'Понятно',
        cancelText: 'Закрыть',
        confirmButtonClass: 'bg-indigo-600 hover:bg-indigo-700 text-white'
      });
      showResult('Длинное сообщение', confirmed);
    });

    // Только подтверждение
    document.getElementById('no-cancel').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Информация',
        message: 'Это информационное сообщение без кнопки отмены.',
        confirmText: 'ОК',
        cancelText: '',
        confirmButtonClass: 'bg-gray-600 hover:bg-gray-700 text-white'
      });
      showResult('Только подтверждение', confirmed);
    });

    // Тест на мобильном
    document.getElementById('mobile-test').addEventListener('click', async () => {
      const confirmed = await window.confirmModal?.show({
        title: 'Мобильный тест',
        message: 'Уменьшите окно браузера, чтобы протестировать адаптивность модального окна на мобильных устройствах.',
        confirmText: 'Протестировано',
        cancelText: 'Отмена',
        confirmButtonClass: 'bg-pink-600 hover:bg-pink-700 text-white'
      });
      showResult('Мобильный тест', confirmed);
    });
  });
</script>
</PageLayout>
