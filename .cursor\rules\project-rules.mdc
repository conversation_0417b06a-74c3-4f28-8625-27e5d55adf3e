---
description: 
globs: 
alwaysApply: false
---
# Проектные Правила: Миграция шаблона AizenHTML-10 на AstroJS

## 🎯 Цель
Перевести шаблон `aizenhtml-10` на современный стек с использованием AstroJS, сохраняя оригинальный визуальный стиль, структуру и логику взаимодействия.

---

## 🔧 Базовые Технологии
- **AstroJS** — основа проекта (Island architecture)
- **TailwindCSS** — для замены Bootstrap и кастомной верстки
- **TypeScript** — основной язык скриптов
- **React** / **Solid** / **VanillaJS** — для динамичных UI-компонентов (если необходимо)
- **Iconify** или встроенные SVG — замена Font Awesome / Stroke-Gap
- **@astrojs/image** — для оптимизации изображений

---

## 📁 Структура проекта
src/
/components/ → все UI-блоки (Navbar, Footer, HeroSection и пр.)
/layouts/ → общие шаблоны страницы
/pages/ → страницы сайта
/styles/ → глобальные и утилитарные стили
assets/ → изображения и шрифты
public/ → статика, не обрабатываемая Astro
data/ → данные для сайта (json-файлы)
docs/ → документация по проекту
aizenhtml-10/ → шаблон проекта используемый для копии

---

## 📌 Правила миграции

1. **HTML → Astro-компоненты**
   - Каждый HTML-фрагмент преобразуется в `.astro` или `.tsx` компонент.
   - Логика разбивается по компонентам: Header, Footer, Section и т.д.

2. **CSS**
   - Все старые CSS-файлы анализируются и заменяются на Tailwind utility-классы.
   - Уникальные стили — переносятся в `styles/custom.css` при необходимости.

3. **Иконки**
   - Заменить Font Awesome и Stroke-Gap иконки на Iconify или встроенные SVG-компоненты.

4. **JS**
   - Вся логика на jQuery/VanillaJS переписывается на компонентный подход (если нужно — React/Solid).

5. **Шрифты**
   - Кастомные шрифты подключаются через `astro:assets` или `public/`.

6. **SEO**
   - Добавить `Astro SEO` интеграцию (`@astrojs/seo` или через layouts/Head.astro)

7. **Accessibility**
   - Все интерактивные компоненты должны быть доступны (aria-* и т.п.)

---

## ✅ Примеры

### Пример: Перевод секции hero

**Было (HTML):**
```html
<section class="hero">
  <h1>Welcome to Aizen</h1>
  <p>Simple. Creative. Elegant.</p>
</section>

---
// src/components/Hero.astro
---
<section class="text-center py-16 bg-gray-100">
  <h1 class="text-4xl font-bold">Welcome to Aizen</h1>
  <p class="text-lg text-gray-600 mt-2">Simple. Creative. Elegant.</p>
</section>


🧠 Инструкции для AI/IDE
- Генерировать код с приоритетом переиспользуемости (компоненты, layouts)
- Не использовать старые CSS/JS библиотеки
- Проверять соответствие дизайну с оригиналом (визуально идентично)
- Пояснять изменения в комментариях, если происходит адаптация логики

🚫 Что не использовать
- Bootstrap, jQuery, Font Awesome (заменить)
- Глобальные CSS классы без явной нужды
- Старые подходы (inline styles, onclick=, data- атрибуты для логики)


📅 Этапы
1. Подготовка структуры проекта (Astro init + Tailwind)
2. Перенос layout и базовых страниц
3. Миграция секций как компонентов
4. Тестирование соответствия дизайну
5. Оптимизация и добавление динамики



























