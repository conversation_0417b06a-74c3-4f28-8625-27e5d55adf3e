---
import type { Product } from '../../types';
import Tooltip from '../ui/Tooltip.astro';

interface Props {
  product: Product;
  'data-size'?: string;
  'data-color'?: string;
  'data-price'?: string | number;
  'data-name'?: string;
}

const { product } = Astro.props;

// Prepare size and color display
const mainSizeValue = product.attributes?.size?.length
  ? `${product.attributes.size.length} x ${product.attributes.size.width} x ${product.attributes.size.height} мм`
  : product.attributes?.size?.variants?.[0]
    ? `${product.attributes.size.variants[0].length} x ${product.attributes.size.variants[0].width} x ${product.attributes.size.variants[0].height} мм`
    : null;

const mainColor = product.attributes?.colors?.[0] || null;
const colorPigments = product.attributes?.color_pigments;

// Prepare color display with pigments and actual colors
const getColorDisplay = () => {
  if (!colorPigments) return mainColor;

  const colors = product.attributes?.colors || [];
  if (colors.length === 0) return colorPigments.name;

  // Determine how many colors to show based on pigment type
  let colorsToShow: string[] = [];
  if (colorPigments.id === 'no_pigment') {
    colorsToShow = [colors[0]]; // Just show the main color (usually gray)
  } else if (colorPigments.id === 'one_pigment') {
    colorsToShow = colors.slice(0, 1); // Show 1 color
  } else if (colorPigments.id === 'two_pigments') {
    colorsToShow = colors.slice(0, 2); // Show 2 colors
  } else if (colorPigments.id === 'three_or_more_pigments') {
    colorsToShow = colors.slice(0, 3); // Show 3 colors
  }

  if (colorsToShow.length === 0) {
    return colorPigments.name;
  }

  return `${colorPigments.name} (${colorsToShow.join(', ')})`;
};

const colorDisplay = getColorDisplay();

// Prepare size data for rendering
let sizeAttributes = null;
if (product.attributes?.size) {
  if (product.attributes.size.variants) {
    sizeAttributes = product.attributes.size.variants;
  } else if (product.attributes.size.length) {
    sizeAttributes = [{ length: product.attributes.size.length, width: product.attributes.size.width }];
  }
}

// Prepare color data for rendering
let colorAttributes = null;
if (product.attributes?.colors && product.attributes.colors.length > 0) {
    colorAttributes = product.attributes.colors;
}

---

{/* Product Card List View */}
<div class="product-card bg-white shadow-md rounded-none overflow-hidden group flex flex-col md:flex-row list-view"
     data-category={product.category}
     data-size={Astro.props['data-size']}
     data-color={Astro.props['data-color']}
     data-price={Astro.props['data-price']}
     data-name={Astro.props['data-name']}
     data-subcategory={product.subcategory.toLowerCase().replace(/\s+/g, '-')}>
  {/* Image Section (clickable) */}
  <a href={`/products/${product.categorySlug}/${product.slug}`} class="relative overflow-hidden flex-shrink-0 w-full md:w-48 aspect-square block">
    <img
      src={`/product/${product.images.main}`}
      alt={product.name}
      class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
    />
  </a>

  {/* Details Section (List View) */}
  <div class="p-4 flex-grow flex flex-col">
    {/* Top row: Name and Price */}
    <div class="flex justify-between items-start mb-2">
      <h3 class="text-xl font-bold text-text-main mr-4 line-clamp-2">
        <a href={`/products/${product.categorySlug}/${product.slug}`} class="hover:underline">{product.name}</a>
      </h3>
      <p class="text-[#baa385] text-2xl font-semibold flex-shrink-0">
        {product.price.value.toFixed(2)} руб. / {product.price.unit}
      </p>
    </div>

    {/* Article */}
    <p class="text-gray-600 text-sm mt-1 mb-1">Арт: {product.id}</p>

    {/* Size */}
    {mainSizeValue && (
      <p class="text-gray-700 text-sm mt-1 mb-1">
        <span class="font-semibold">Размер:</span> {mainSizeValue}
      </p>
    )}

    {/* Color */}
    {(mainColor || colorPigments) && (
      <p class="text-gray-700 text-sm mt-1 mb-1">
        <span class="font-semibold">Цвет:</span> {colorDisplay}
        {colorPigments && (
          <Tooltip
            content={colorPigments.description}
            position="top"
            size="md"
            triggerClass="ml-1"
          />
        )}
      </p>
    )}

    {/* Short Description */}
    {product.shortDescription && (
      <p class="text-gray-700 text-sm mt-2 mb-4 line-clamp-3">{product.shortDescription}</p>
    )}

    {/* Buttons */}
    <div class="flex space-x-2 mt-auto">
      <a href={`/products/${product.categorySlug}/${product.slug}`} class="flex-grow border border-[#baa385] bg-white text-[#baa385] font-medium py-2.5 sm:py-2.5 lg:py-2 text-center hover:bg-gray-50 transition-colors">Подробнее</a>
      <a href="/request" class="flex-grow bg-[#baa385] text-white font-medium py-2.5 sm:py-2.5 lg:py-2 text-center hover:bg-[#a89274] transition-colors">Заказать</a>
    </div>
  </div>
</div>
