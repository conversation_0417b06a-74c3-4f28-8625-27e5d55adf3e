---
import PageLayout from '../layouts/PageLayout.astro';
import Hero from '../components/hero/Hero.astro';
import AboutSection from '../components/about/AboutSection.astro';
import Features from '../components/features/Features.astro';
import ProductsCollage from '../components/products/ProductsCollage.astro';
import ClientsSection from '../components/clients/ClientsSection.astro';
---

<PageLayout title="Aizen Architecture Template" description="Главная страница Aizen" hideTitleSection={true}>
  <Hero
    background="/images/main-slider/image-1.jpg"
    title="Плитка тротуарная, бордюры, брусчатка, отливы ..."
    subtitle="Вся продукция из высокопрочного бетона, производство в Осиповичах"
  />

  {/* Секция О нас - Производство */}
  <AboutSection />

  {/* Секция Характеристики продукции */}
  <Features />

  {/* Секция Наша продукция (коллаж) */}
  <ProductsCollage />

  {/* Секция Клиенты и отзывы */}
  <ClientsSection />
</PageLayout>

<script>
  // Preloader
  document.addEventListener('DOMContentLoaded', () => {
    const preloader = document.querySelector('.preloader');
    if (preloader) {
      preloader.classList.add('active');
      setTimeout(() => {
        preloader.classList.remove('active');
      }, 1000);
    }
  });
</script> 