// Global tooltip functionality using event delegation
function initTooltips() {
  console.log('Initializing tooltips...');

  // Remove existing listeners to prevent duplicates
  document.removeEventListener('click', handleTooltipClick);
  document.removeEventListener('mouseover', handleTooltipMouseEnter);
  document.removeEventListener('mouseout', handleTooltipMouseLeave);

  // Add event listeners using delegation
  document.addEventListener('click', handleTooltipClick);
  document.addEventListener('mouseover', handleTooltipMouseEnter);
  document.addEventListener('mouseout', handleTooltipMouseLeave);

  console.log('Tooltips initialized successfully');
}

function handleTooltipClick(e) {
  // Check if clicked element is a tooltip trigger
  if (e.target.closest('.tooltip-trigger')) {
    console.log('Tooltip trigger clicked');
    e.preventDefault();
    e.stopPropagation();

    const container = e.target.closest('.tooltip-container');
    if (!container) {
      console.log('No tooltip container found');
      return;
    }

    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) {
      console.log('No tooltip element found');
      console.log('Container HTML:', container.innerHTML);
      console.log('Container classes:', container.className);
      return;
    }

    console.log('Tooltip found, toggling visibility');

    // Hide all other tooltips
    document.querySelectorAll('.tooltip').forEach(t => {
      if (t !== tooltip) {
        t.style.opacity = '0';
        t.style.pointerEvents = 'none';
      }
    });

    // Toggle current tooltip
    if (tooltip.style.opacity === '1') {
      console.log('Hiding tooltip');
      tooltip.style.opacity = '0';
      tooltip.style.pointerEvents = 'none';
    } else {
      console.log('Showing tooltip');
      tooltip.style.opacity = '1';
      tooltip.style.pointerEvents = 'auto';
    }
  }
  // Hide tooltips when clicking outside
  else if (!e.target.closest('.tooltip-container')) {
    document.querySelectorAll('.tooltip').forEach(tooltip => {
      tooltip.style.opacity = '0';
      tooltip.style.pointerEvents = 'none';
    });
  }
}

function handleTooltipMouseEnter(e) {
  if (e.target.closest('.tooltip-trigger')) {
    console.log('Mouse entered tooltip trigger');
    const container = e.target.closest('.tooltip-container');
    if (!container) return;

    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    console.log('Showing tooltip on hover');
    tooltip.style.opacity = '1';
    tooltip.style.pointerEvents = 'auto';
  }
}

function handleTooltipMouseLeave(e) {
  if (e.target.closest('.tooltip-trigger')) {
    console.log('Mouse left tooltip trigger');
    const container = e.target.closest('.tooltip-container');
    if (!container) return;

    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    console.log('Hiding tooltip on mouse leave');
    tooltip.style.opacity = '0';
    tooltip.style.pointerEvents = 'none';
  }
}

// Initialize tooltips when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing tooltips...');

  // Check if tooltip elements exist
  const tooltipContainers = document.querySelectorAll('.tooltip-container');
  const tooltipTriggers = document.querySelectorAll('.tooltip-trigger');
  const tooltips = document.querySelectorAll('.tooltip');

  console.log(`Found ${tooltipContainers.length} tooltip containers`);
  console.log(`Found ${tooltipTriggers.length} tooltip triggers`);
  console.log(`Found ${tooltips.length} tooltip elements`);

  initTooltips();
});

// Also try to initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
  // DOM is still loading
  document.addEventListener('DOMContentLoaded', initTooltips);
} else {
  // DOM is already loaded
  console.log('DOM already loaded, initializing tooltips immediately...');
  initTooltips();
}

// Re-initialize tooltips when new content is added (for dynamic content)
window.initTooltips = initTooltips;
