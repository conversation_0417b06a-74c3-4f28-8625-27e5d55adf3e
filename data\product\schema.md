# Схема данных товаров LuxBeton

Данный документ описывает структуру JSON-файла для хранения информации о товарах компании LuxBeton.

## Основная структура

JSON-файл представляет собой массив объектов, где каждый объект - это отдельный товар со следующими полями:

```json
{
  "id": "string",                // Уникальный артикул товара
  "name": "string",              // Название товара
  "category": "string",          // Основная категория товара
  "subcategory": "string",       // Подкатегория товара
  "shortDescription": "string",  // Краткое описание товара
  "fullDescription": "string",   // Полное описание товара
  "price": {                     // Информация о цене
    "unit": "string",           // Единица измерения (шт, м²)
    "value": number             // Стоимость в рублях
  },
  "attributes": {                // Атрибуты товара
    "colors": ["string"],       // Доступные цвета
    "texture": "string",        // Тип текстуры/фактуры
    "size": {                   // Размеры товара в мм
      "length": number,         // Длина
      "width": number,          // Ширина
      "height": number          // Высота
    },
    "weight": number,           // Вес в кг
    "strength": "string"        // Класс прочности
  },
  "images": {                    // Изображения товара
    "main": "string",           // Путь к главному изображению
    "additional": ["string"]    // Пути к дополнительным изображениям (до 6 шт)
  },
  "inStock": boolean,            // Наличие товара на складе
  "popularity": number           // Рейтинг популярности (от 0 до 5)
}
```

## Соглашение об именовании файлов

Все медиафайлы хранятся в директории `/public/product/` и организованы по следующему принципу:

1. Для каждого товара создается отдельная папка с именем, соответствующим ID товара (например, `TB-001`)
2. Внутри папки товара:
   - `_main.jpg` - главное изображение товара
   - `_1.jpg`, `_2.jpg`, ... - дополнительные изображения товара

Пример пути к изображению: `/TB-001/main.jpg` (относительно директории `/public/product/`)

## Категории товаров

Основные категории товаров:
- Тротуарная плитка
- Брусчатка
- Бордюры
- Водостоки
- Ступени
- Заборы
- Малые архитектурные формы

## Примечания по использованию

1. Все изображения должны быть в формате JPG или PNG
2. Размеры указываются в миллиметрах
3. Вес указывается в килограммах
4. Цены указываются в рублях
5. ID товара должен быть уникальным и соответствовать формату "XX-NNN", где XX - код категории, NNN - порядковый номер
