import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const categoriesPath = path.join(__dirname, '../../../../data/product/categories.json');

export async function GET() {
  try {
    const data = await fs.readFile(categoriesPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения файла' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const data = await fs.readFile(categoriesPath, 'utf-8');
    const categoriesData = JSON.parse(data);
    
    // Добавление новой категории
    categoriesData.categories.push(body);
    
    await fs.writeFile(categoriesPath, JSON.stringify(categoriesData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const data = await fs.readFile(categoriesPath, 'utf-8');
    let categoriesData = JSON.parse(data);
    
    // Обновление существующей категории
    categoriesData.categories = categoriesData.categories.map(category => 
      category.id === body.id ? body : category
    );
    
    await fs.writeFile(categoriesPath, JSON.stringify(categoriesData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка обновления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    
    const data = await fs.readFile(categoriesPath, 'utf-8');
    let categoriesData = JSON.parse(data);
    
    // Удаление категории
    categoriesData.categories = categoriesData.categories.filter(category => category.id !== id);
    
    await fs.writeFile(categoriesPath, JSON.stringify(categoriesData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка удаления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
