---
export interface Props {
  title: string;
  value: string | number;
  description?: string;
  icon?: string;
  iconColor?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  class?: string;
}

const { 
  title, 
  value, 
  description, 
  icon, 
  iconColor = 'bg-blue-500',
  trend,
  class: className = '' 
} = Astro.props;
---

<div class={`relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:px-6 sm:py-6 ${className}`}>
  <dt>
    {icon && (
      <div class={`absolute rounded-md ${iconColor} p-3`}>
        <Fragment set:html={icon} />
      </div>
    )}
    <p class={`${icon ? 'ml-16' : ''} truncate text-sm font-medium text-gray-500`}>{title}</p>
  </dt>
  <dd class={`${icon ? 'ml-16' : ''} flex items-baseline`}>
    <p class="text-2xl font-semibold text-gray-900">{value}</p>
    {trend && (
      <p class={`ml-2 flex items-baseline text-sm font-semibold ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
        <svg class={`-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center ${trend.isPositive ? 'text-green-500' : 'text-red-500'}`} fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d={trend.isPositive ? "M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" : "M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"} clip-rule="evenodd" />
        </svg>
        <span class="sr-only">{trend.isPositive ? 'Increased' : 'Decreased'} by</span>
        {Math.abs(trend.value)}%
      </p>
    )}
  </dd>
  {description && (
    <div class={`${icon ? 'ml-16' : ''} mt-1`}>
      <p class="text-xs text-gray-500">{description}</p>
    </div>
  )}
</div>
