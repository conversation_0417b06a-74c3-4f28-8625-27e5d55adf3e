---
const socialLinks = [
  { icon: "mdi:instagram", url: "https://www.instagram.com/stoneartbel?igsh=MXY5dGgzZ3d2djVn" },
  { icon: "mdi:telegram", url: "https://www.instagram.com/stoneartbel?igsh=MXY5dGgzZ3d2djVn" },
  { icon: "mdi:viber", url: "https://www.instagram.com/stoneartbel?igsh=MXY5dGgzZ3d2djVn" }
];
---

<div class="flex space-x-2">
  {socialLinks.map((link) => (
    <a
      href={link.url}
      class="text-white hover:text-primary transition-colors duration-200 text-xl"
      target="_blank"
      rel="noopener noreferrer"
    >
      <span class="iconify" data-icon={link.icon}></span>
    </a>
  ))}
</div> 