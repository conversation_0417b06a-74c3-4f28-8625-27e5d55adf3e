---
export interface Props {
  name?: string;
  id?: string;
  class?: string;
  disabled?: boolean;
  required?: boolean;
  value?: string;
}

const {
  name,
  id,
  class: className = '',
  disabled = false,
  required = false,
  value
} = Astro.props;

const baseClasses = 'flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors';

const classes = `${baseClasses} ${className}`;
---

<select
  name={name}
  id={id}
  class={classes}
  disabled={disabled}
  required={required}
  value={value}
>
  <slot />
</select>
