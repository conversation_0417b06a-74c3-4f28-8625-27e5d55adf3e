import { isAuthenticated } from '../../../utils/auth.js';
import { autoDetectProductImages } from '../../../utils/imageUtils.js';

/**
 * API для автоматического определения изображений товара
 * GET /api/admin/detect-images?productId=TB-001
 */
export async function GET({ request, url }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const productId = url.searchParams.get('productId');
    
    if (!productId) {
      return new Response(JSON.stringify({ error: 'Не указан ID товара' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Автоматически определяем изображения
    const images = await autoDetectProductImages(productId);

    return new Response(JSON.stringify({
      success: true,
      images
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при определении изображений:', error);
    return new Response(JSON.stringify({ 
      error: 'Ошибка при определении изображений',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
