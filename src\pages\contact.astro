---
import PageLayout from '../layouts/PageLayout.astro';

const contactInfo = [
  {
    icon: "mdi:map-marker",
    title: "Our Location",
    content: "123 Architecture Street, Design District, City, Country"
  },
  {
    icon: "mdi:phone",
    title: "Phone Number",
    content: "+****************"
  },
  {
    icon: "mdi:email",
    title: "Email Address",
    content: "<EMAIL>"
  },
  {
    icon: "mdi:clock",
    title: "Working Hours",
    content: "Mon - Fri: 9:00 AM - 6:00 PM"
  }
];
---

<PageLayout 
  title="КОНТАКТЫ"
  breadcrumbs={[
    { text: 'Контакты', url: '/contact' }
  ]}
>

  <!-- Contact Information -->
  <section class="py-16">
    <div class="container">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {contactInfo.map((info) => (
          <div class="text-center p-6 bg-white rounded-lg shadow-md">
            <iconify-icon icon={info.icon} class="text-4xl text-blue-600 mb-4"></iconify-icon>
            <h3 class="text-xl font-bold mb-2">{info.title}</h3>
            <p class="text-gray-600">{info.content}</p>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Contact Form and Map -->
  <section class="py-16 bg-gray-50">
    <div class="container">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Contact Form -->
        <div class="bg-white p-8 rounded-lg shadow-md">
          <h2 class="text-2xl font-bold mb-6">Send Us a Message</h2>
          <form id="contactForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Your Name</label>
                <input 
                  type="text" 
                  id="name" 
                  name="name" 
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                  required
                >
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Your Email</label>
                <input 
                  type="email" 
                  id="email" 
                  name="email" 
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                  required
                >
              </div>
            </div>
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
              <input 
                type="text" 
                id="subject" 
                name="subject" 
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                required
              >
            </div>
            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Your Message</label>
              <textarea 
                id="message" 
                name="message" 
                rows="6" 
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                required
              ></textarea>
            </div>
            <button 
              type="submit" 
              class="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors"
            >
              Send Message
            </button>
          </form>
        </div>

        <!-- Map -->
        <div class="h-[500px] rounded-lg overflow-hidden shadow-md">
          <iframe 
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387193.30591910525!2d-74.25986432970718!3d40.697149422113014!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2s!4v1645564750981!5m2!1sen!2s" 
            width="100%" 
            height="100%" 
            style="border:0;" 
            allowfullscreen="" 
            loading="lazy"
          ></iframe>
        </div>
      </div>
    </div>
  </section>
</PageLayout>

<script>
  const form = document.getElementById('contactForm') as HTMLFormElement;
  
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    try {
      // Here you would typically send the data to your backend
      console.log('Form submitted:', data);
      
      // Show success message
      alert('Thank you for your message! We will get back to you soon.');
      form.reset();
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('There was an error sending your message. Please try again.');
    }
  });
</script> 