# Медиафайлы товаров LuxBeton

Эта директория содержит изображения товаров компании LuxBeton. Каждый товар имеет свою папку с изображениями, названную в соответствии с ID товара.

## Структура директории

```
/product/
├── TB-001/                          # Папка с изображениями товара TB-001
│   ├── klassika-plitka_main.jpg     # Главное изображение товара
│   ├── klassika-plitka_1.jpg        # Дополнительное изображение 1
│   ├── klassika-plitka_2.jpg        # Дополнительное изображение 2
│   └── klassika-plitka_3.jpg        # Дополнительное изображение 3
├── BR-003/                          # Папка с изображениями товара BR-003
│   ├── kamenny-cvetok_main.png      # Главное изображение товара
│   ├── kamenny-cvetok_1.png         # Дополнительное изображение 1
│   └── kamenny-cvetok_2.png         # Дополнительное изображение 2
└── ...                              # Папки других товаров
```



## Требования к изображениям

### Форматы и размеры

- **Формат**: JPG или PNG
- **Разрешение**: рекомендуемое разрешение для главного изображения - 1200x800 пикселей
- **Размер файла**: не более 2 МБ для одного изображения

### Именование файлов

Для каждого товара необходимо создать отдельную папку с именем, соответствующим ID товара (например, `TB-001`). Внутри папки должны находиться следующие файлы:

#### Правила именования изображений:

**Главное изображение:**
- Должно заканчиваться на `_main` перед расширением файла
- Примеры: `plitka-crewz_main.png`, `bruschatka-elit_main.jpg`, `TB-001_main.jpg`
- Может иметь любое описательное название, но обязательно должно содержать `_main` в конце

**Дополнительные изображения:**
- Должны заканчиваться на `_1`, `_2`, `_3` и т.д. перед расширением файла
- Примеры: `plitka-crewz_1.png`, `plitka-crewz_2.jpg`, `bruschatka-elit_1.jpg`
- Нумерация начинается с 1 и идет по порядку (до 6 дополнительных изображений)

#### Примеры правильного именования:
```
TB-001/
├── klassika-plitka_main.jpg     # Главное изображение
├── klassika-plitka_1.jpg        # Дополнительное изображение 1
├── klassika-plitka_2.jpg        # Дополнительное изображение 2
└── klassika-plitka_3.jpg        # Дополнительное изображение 3

BR-003/
├── kamenny-cvetok_main.png      # Главное изображение
├── kamenny-cvetok_1.png         # Дополнительное изображение 1
└── kamenny-cvetok_2.png         # Дополнительное изображение 2
```



### Требования к содержанию изображений

1. **Главное изображение** должно четко показывать товар, желательно на нейтральном фоне или в реальном применении
2. **Дополнительные изображения** могут включать:
   - Товар с разных ракурсов
   - Товар в различных цветовых вариантах
   - Примеры использования товара в реальных проектах
   - Детали и текстуру товара крупным планом
   - Процесс установки или монтажа

## Рекомендации по фотографированию

1. Используйте хорошее освещение, предпочтительно естественный свет
2. Фотографируйте товар на нейтральном фоне для главного изображения
3. Убедитесь, что товар чистый и не имеет видимых дефектов
4. Для демонстрации масштаба можно использовать линейку или другие предметы известного размера
5. Фотографии реальных проектов с использованием товара повышают привлекательность для клиентов

## Процесс добавления изображений

1. **Создайте папку** с именем, соответствующим ID товара (например, `TB-001`)
2. **Подготовьте и оптимизируйте изображения** согласно требованиям к размеру и качеству
3. **Именуйте файлы** согласно новым правилам:
   - Главное изображение: `описательное-название_main.jpg/png`
   - Дополнительные: `описательное-название_1.jpg/png`, `описательное-название_2.jpg/png` и т.д.
4. **Разместите изображения** в созданной папке
5. **Обновите данные товара** в админ-панели или файле `products.json`

### Автоматическое определение изображений

Система автоматически определяет изображения товара по следующим правилам:
- **Главное изображение**: ищется файл, заканчивающийся на `_main`
- **Дополнительные изображения**: ищутся файлы, заканчивающиеся на `_1`, `_2`, `_3` и т.д.

## Примечание

Все пути к изображениям в файле `products.json` указываются относительно директории `/public/product/`, например: `TB-001/klassika-plitka_main.jpg`
