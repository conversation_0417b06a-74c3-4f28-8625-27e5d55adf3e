---
import PageLayout from '../layouts/PageLayout.astro';
import ToastNotification from '../components/ui/ToastNotification.astro';
---

<PageLayout title="Демо Toast уведомлений">
  <section class="py-16">
    <div class="container mx-auto px-4 max-w-4xl">
      <h1 class="text-3xl font-bold text-center mb-8">Демо Toast уведомлений</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Основные типы -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h2 class="text-xl font-semibold mb-4">Основные типы</h2>
          <div class="space-y-3">
            <button 
              id="success-toast" 
              class="w-full px-4 py-2 bg-green-100 text-green-700 border border-green-300 rounded hover:bg-green-200 transition-colors"
            >
              Успех
            </button>
            
            <button 
              id="error-toast" 
              class="w-full px-4 py-2 bg-red-100 text-red-700 border border-red-300 rounded hover:bg-red-200 transition-colors"
            >
              Ошибка
            </button>
            
            <button 
              id="warning-toast" 
              class="w-full px-4 py-2 bg-yellow-100 text-yellow-700 border border-yellow-300 rounded hover:bg-yellow-200 transition-colors"
            >
              Предупреждение
            </button>
            
            <button 
              id="info-toast" 
              class="w-full px-4 py-2 bg-blue-100 text-blue-700 border border-blue-300 rounded hover:bg-blue-200 transition-colors"
            >
              Информация
            </button>
          </div>
        </div>

        <!-- Различные длительности -->
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h2 class="text-xl font-semibold mb-4">Различные длительности</h2>
          <div class="space-y-3">
            <button 
              id="short-toast" 
              class="w-full px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
            >
              Короткое (1 сек)
            </button>
            
            <button 
              id="normal-toast" 
              class="w-full px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
            >
              Обычное (2 сек)
            </button>
            
            <button 
              id="long-toast" 
              class="w-full px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
            >
              Длинное (5 сек)
            </button>
            
            <button 
              id="multiple-toast" 
              class="w-full px-4 py-2 bg-purple-100 text-purple-700 border border-purple-300 rounded hover:bg-purple-200 transition-colors"
            >
              Несколько подряд
            </button>
          </div>
        </div>
      </div>

      <!-- Тестирование админ утилит -->
      <div class="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Тестирование админ утилит</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button 
            id="admin-success" 
            class="px-4 py-2 bg-green-100 text-green-700 border border-green-300 rounded hover:bg-green-200 transition-colors"
          >
            adminModal.showSuccess()
          </button>
          
          <button 
            id="admin-error" 
            class="px-4 py-2 bg-red-100 text-red-700 border border-red-300 rounded hover:bg-red-200 transition-colors"
          >
            adminModal.showError()
          </button>
          
          <button 
            id="clear-all" 
            class="px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
          >
            Очистить все
          </button>
        </div>
      </div>

      <!-- Инструкции -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 class="text-lg font-semibold mb-2 text-blue-800">Особенности Toast уведомлений:</h3>
        <ul class="text-blue-700 space-y-1">
          <li>• <strong>Десктоп:</strong> Появляются внизу справа экрана</li>
          <li>• <strong>Мобильные:</strong> Появляются внизу по центру экрана</li>
          <li>• <strong>Автоматическое исчезновение:</strong> Через заданное время</li>
          <li>• <strong>Ручное закрытие:</strong> Кнопка X в правом верхнем углу</li>
          <li>• <strong>Анимации:</strong> Плавное появление и исчезновение</li>
          <li>• <strong>Стекинг:</strong> Несколько уведомлений располагаются друг под другом</li>
        </ul>
      </div>
    </div>
  </section>

  <!-- Toast компонент -->
  <ToastNotification />
</PageLayout>

<script>
  // Ждем загрузки DOM
  document.addEventListener('DOMContentLoaded', () => {
    // Основные типы
    document.getElementById('success-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Операция выполнена успешно!',
        type: 'success',
        title: 'Успех'
      });
    });

    document.getElementById('error-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Произошла ошибка при выполнении операции',
        type: 'error',
        title: 'Ошибка'
      });
    });

    document.getElementById('warning-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Внимание! Это действие может быть необратимым',
        type: 'warning',
        title: 'Предупреждение'
      });
    });

    document.getElementById('info-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Новая информация доступна для просмотра',
        type: 'info',
        title: 'Информация'
      });
    });

    // Различные длительности
    document.getElementById('short-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Быстрое уведомление',
        type: 'info',
        duration: 1000
      });
    });

    document.getElementById('normal-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Обычное уведомление',
        type: 'success',
        duration: 2000
      });
    });

    document.getElementById('long-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Длинное уведомление для важной информации',
        type: 'warning',
        duration: 5000
      });
    });

    document.getElementById('multiple-toast').addEventListener('click', () => {
      window.toastManager?.show({
        message: 'Первое уведомление',
        type: 'success'
      });
      
      setTimeout(() => {
        window.toastManager?.show({
          message: 'Второе уведомление',
          type: 'info'
        });
      }, 500);
      
      setTimeout(() => {
        window.toastManager?.show({
          message: 'Третье уведомление',
          type: 'warning'
        });
      }, 1000);
    });

    // Тестирование админ утилит
    document.getElementById('admin-success').addEventListener('click', async () => {
      if (window.adminModal) {
        await window.adminModal.showSuccess('Товар успешно создан!');
      } else {
        window.toastManager?.show({
          message: 'adminModal не доступен (только в админ-панели)',
          type: 'error'
        });
      }
    });

    document.getElementById('admin-error').addEventListener('click', async () => {
      if (window.adminModal) {
        await window.adminModal.showError('Не удалось сохранить изменения');
      } else {
        window.toastManager?.show({
          message: 'adminModal не доступен (только в админ-панели)',
          type: 'error'
        });
      }
    });

    document.getElementById('clear-all').addEventListener('click', () => {
      window.toastManager?.clear();
    });
  });
</script>
</PageLayout>
