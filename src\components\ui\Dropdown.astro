---
export interface Props {
  trigger: string;
  class?: string;
  align?: 'left' | 'right';
  size?: 'sm' | 'md' | 'lg';
}

const { 
  trigger, 
  class: className = '',
  align = 'right',
  size = 'md'
} = Astro.props;

const alignClasses = {
  left: 'left-0',
  right: 'right-0'
};

const sizeClasses = {
  sm: 'w-48',
  md: 'w-56',
  lg: 'w-64'
};
---

<div class={`relative inline-block text-left ${className}`}>
  <div>
    <button 
      type="button" 
      class="inline-flex items-center justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
      id="dropdown-button"
    >
      <Fragment set:html={trigger} />
      <svg class="ml-2 -mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
      </svg>
    </button>
  </div>

  <div 
    class={`hidden absolute ${alignClasses[align]} z-10 mt-2 ${sizeClasses[size]} rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none`}
    id="dropdown-menu"
  >
    <div class="py-1">
      <slot />
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('dropdown-button');
    const menu = document.getElementById('dropdown-menu');

    button?.addEventListener('click', function(e) {
      e.stopPropagation();
      menu?.classList.toggle('hidden');
    });

    // Закрытие при клике вне меню
    document.addEventListener('click', function() {
      menu?.classList.add('hidden');
    });

    // Предотвращение закрытия при клике внутри меню
    menu?.addEventListener('click', function(e) {
      e.stopPropagation();
    });
  });
</script>
