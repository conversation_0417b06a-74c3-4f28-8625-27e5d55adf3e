---
// Простая страница входа без серверной обработки
---

<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Вход в админ-панель | LuxBeton</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
  <div class="max-w-md w-full bg-white rounded-lg shadow-md p-8">
    <h1 class="text-2xl font-bold text-center mb-6">Вход в админ-панель</h1>

    <div id="error-message" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden">
    </div>

    <form id="login-form" class="space-y-4">
      <div>
        <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Имя пользователя</label>
        <input
          type="text"
          id="username"
          name="username"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Пароль</label>
        <input
          type="password"
          id="password"
          name="password"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <button
        type="submit"
        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        Войти
      </button>
    </form>
  </div>

  <script>
    document.getElementById('login-form').addEventListener('submit', async (e) => {
      e.preventDefault();

      const errorDiv = document.getElementById('error-message');
      const submitBtn = e.target.querySelector('button[type="submit"]');

      // Скрываем предыдущие ошибки
      errorDiv.classList.add('hidden');

      // Показываем состояние загрузки
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Вход...';
      submitBtn.disabled = true;

      try {
        const formData = new FormData(e.target);

        const response = await fetch('/api/admin/auth', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          window.location.href = result.redirect || '/admin';
        } else {
          errorDiv.textContent = result.error || 'Ошибка входа';
          errorDiv.classList.remove('hidden');
        }
      } catch (error) {
        console.error('Ошибка:', error);
        errorDiv.textContent = 'Ошибка соединения с сервером';
        errorDiv.classList.remove('hidden');
      } finally {
        // Восстанавливаем кнопку
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
      }
    });
  </script>
</body>
</html>
