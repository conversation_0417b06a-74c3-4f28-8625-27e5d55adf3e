---
const allMenuItems = [
  { text: "ПРОИЗВОДСТВО", url: "/about" },
  { text: "ПРОДУКЦИЯ", url: "/products" },
  { text: "УСЛУГИ", url: "/services" },
  { text: "БЛОГ", url: "/blog" },
  { text: "КОНТАКТЫ", url: "/contact" }
];

const mainTabletMenuItems = [
  { text: "ПРОДУКЦИЯ", url: "/products" },
  { text: "УСЛУГИ", url: "/services" },
  { text: "КОНТАКТЫ", url: "/contact" },
];

const moreMenuItems = [
    { text: "ПРОИЗВОДСТВО", url: "/about" },
    { text: "БЛОГ", url: "/blog" },
]
---

<nav class="relative">
  <!-- Mobile menu button -->
  <button
    class="md:hidden p-2 rounded-md hover:bg-primary/10 focus:outline-none"
    aria-label="Открыть меню"
    id="mobile-menu-open-btn"
  >
    <svg
      class="h-7 w-7 text-text-main"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M4 6h16M4 12h16M4 18h16"
      />
    </svg>
  </button>

  <!-- Desktop menu -->
  <div id="desktop-menu">
    <!-- Full menu for large screens -->
    <ul class="hidden lg:flex space-x-10 pt-[15px]">
      {allMenuItems.map((item) => (
        <li>
          <a
            href={item.url}
            class="font-sans font-semibold text-[16px] text-text-main px-2 py-2 transition-colors duration-200 border-b-2 border-transparent hover:text-primary hover:border-primary focus:text-primary focus:border-primary"
            >{item.text}</a
          >
        </li>
      ))}
    </ul>

    <!-- Tablet menu -->
    <ul class="hidden md:flex lg:hidden space-x-4 pt-[15px] justify-end items-center">
        {mainTabletMenuItems.map((item) => (
            <li>
            <a
                href={item.url}
                class="font-sans font-semibold text-[16px] text-text-main px-2 py-2 transition-colors duration-200 border-b-2 border-transparent hover:text-primary hover:border-primary focus:text-primary focus:border-primary"
                >{item.text}</a
            >
            </li>
        ))}
        <li class="relative">
            <button id="more-menu-btn" class="flex items-center font-sans font-semibold text-[16px] text-gray-400 px-2 py-2 transition-colors duration-200 border-b-2 border-transparent hover:text-primary hover:border-primary focus:text-primary focus:border-primary">
                Еще
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
            </button>
            <ul id="more-menu-dropdown" class="absolute hidden bg-white shadow-lg mt-2 py-1 w-auto z-20 top-full right-0">
                {moreMenuItems.map((item) => (
                    <li>
                        <a href={item.url} class="block font-sans font-semibold px-4 py-2 text-[16px] text-text-main hover:bg-primary hover:text-white">{item.text}</a>
                    </li>
                ))}
            </ul>
        </li>
    </ul>
  </div>

  <!-- Mobile menu overlay -->
  <div id="mobile-menu-overlay" class="hidden fixed inset-0 z-40 bg-black/40"></div>
  <!-- Mobile menu drawer -->
  <aside id="mobile-menu-drawer" class="fixed top-0 right-0 z-50 w-4/5 max-w-xs h-full bg-white shadow-lg transform translate-x-full transition-transform duration-300 flex flex-col">
    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-100">
      <span class="font-bold text-lg text-text-main">Меню</span>
      <button aria-label="Закрыть меню" class="p-2" id="mobile-menu-close-btn">
        <svg class="h-7 w-7 text-text-main" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    <ul class="flex-1 flex flex-col divide-y divide-gray-100 overflow-y-auto">
      {allMenuItems.map((item) => (
        <li>
          <a
            href={item.url}
            class="block font-sans font-semibold text-[18px] text-text-main px-6 py-4 hover:text-primary transition-colors duration-200"
            >{item.text}</a
          >
        </li>
      ))}
    </ul>
  </aside>
</nav>

<script>
  const openBtn = document.getElementById('mobile-menu-open-btn');
  const closeBtn = document.getElementById('mobile-menu-close-btn');
  const overlay = document.getElementById('mobile-menu-overlay');
  const drawer = document.getElementById('mobile-menu-drawer');

  function toggleMenu() {
    if (drawer!.classList.contains('translate-x-full')) {
      // Menu is closed, open it
      document.body.style.overflow = 'hidden';
      overlay!.classList.remove('hidden');
      drawer!.classList.remove('translate-x-full');
    } else {
      // Menu is open, close it
      document.body.style.overflow = '';
      overlay!.classList.add('hidden');
      drawer!.classList.add('translate-x-full');
    }
  }

  if (openBtn && closeBtn && overlay && drawer) {
    openBtn.addEventListener('click', toggleMenu);
    closeBtn.addEventListener('click', toggleMenu);
    overlay.addEventListener('click', toggleMenu);
    window.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !drawer!.classList.contains('translate-x-full')) {
        toggleMenu();
      }
    });

    // Close mobile menu when clicking outside the drawer
    document.addEventListener('click', (event) => {
      if (event.target instanceof Node && !drawer.contains(event.target) && event.target !== openBtn) {
        if (!drawer!.classList.contains('translate-x-full')) {
          toggleMenu();
        }
      }
    });
  }

  const moreMenuBtn = document.getElementById('more-menu-btn');
  const moreMenuDropdown = document.getElementById('more-menu-dropdown');

  if (moreMenuBtn && moreMenuDropdown) {
    moreMenuBtn.addEventListener('click', () => {
        moreMenuDropdown.classList.toggle('hidden');
    });

    document.addEventListener('click', (event) => {
        if (event.target instanceof Node && !moreMenuBtn.contains(event.target) && !moreMenuDropdown.contains(event.target)) {
            moreMenuDropdown.classList.add('hidden');
        }
    });
  }
</script>

<style>
@keyframes fade-in {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
.animate-fade-in {
  animation: fade-in 0.25s ease;
}
</style>
