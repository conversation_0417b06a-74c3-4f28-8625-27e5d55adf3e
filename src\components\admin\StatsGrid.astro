---
import Card from '../ui/Card.astro';
import CardContent from '../ui/CardContent.astro';
import Badge from '../ui/Badge.astro';

export interface StatItem {
  title: string;
  value: string | number;
  description?: string;
  icon: string;
  iconColor: string;
  change?: {
    value: number;
    isPositive: boolean;
  };
}

export interface Props {
  stats: StatItem[];
  class?: string;
}

const { stats, class: className = '' } = Astro.props;
---

<div class={`grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 ${className}`}>
  {stats.map(stat => (
    <Card class="hover:shadow-md transition-shadow duration-200">
      <CardContent class="p-6">
        <div class="flex items-center justify-center">
          <div class={`rounded-lg ${stat.iconColor} p-3 shadow-sm`}>
            <Fragment set:html={stat.icon} />
          </div>
          <div class="ml-4 flex-1 text-center">
            <div class="flex items-center justify-center space-x-2">
              <span class="text-2xl font-bold text-gray-900">{stat.value}</span>
              <span class="text-sm font-medium text-gray-500">{stat.title}</span>
              {stat.change && (
                <Badge
                  variant={stat.change.isPositive ? 'success' : 'destructive'}
                  class="ml-1"
                >
                  <svg class={`h-3 w-3 mr-1 ${stat.change.isPositive ? 'rotate-0' : 'rotate-180'}`} fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  {Math.abs(stat.change.value)}%
                </Badge>
              )}
            </div>
            {stat.description && (
              <p class="text-xs text-gray-500 mt-2">{stat.description}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
