---

import MainLayout from './MainLayout.astro';
import Header from '../components/header/Header.astro';
import Footer from '../components/footer/Footer.astro';

interface Props {
  title: string;
  description?: string;
  pageTitle?: string;
  showBreadcrumbs?: boolean;
  breadcrumbs?: Array<{ text: string; url: string }>;
  showSidebar?: boolean;
  hideTitleSection?: boolean;
}

const {
  title,
  description,
  pageTitle,
  showBreadcrumbs = true,
  breadcrumbs = [],
  showSidebar = true,
  hideTitleSection = false
} = Astro.props;
---

<MainLayout title={title} description={description}>
  <Header slot="header" />
  
  {/* Page Title Section */}
  {hideTitleSection ? null : (
    <section class="bg-gray-100 py-8 sm:py-10 lg:py-14">
      <div class="container mx-auto px-4 text-center">
        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold">{pageTitle || title}</h1>
        {showBreadcrumbs && breadcrumbs.length > 0 && (
          <nav class="flex justify-center mt-1 sm:mt-1.5 lg:mt-2" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-xs sm:text-sm text-gray-600">
              <li>
                <a href="/" class="hover:text-primary">Главная</a>
              </li>
              {breadcrumbs.map((item, index) => (
                <li class="flex items-center space-x-2">
                  <span class="mx-2">></span>
                  {index === breadcrumbs.length - 1 ? (
                    <span class="text-primary">{item.text}</span>
                  ) : (
                    <a href={item.url} class="hover:text-primary">
                      {item.text}
                    </a>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}
      </div>
    </section>
  )}

  <slot />

  <Footer slot="footer" />
</MainLayout> 