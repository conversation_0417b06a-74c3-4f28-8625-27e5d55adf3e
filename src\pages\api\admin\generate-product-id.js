import { isAuthenticated } from '../../../utils/auth.js';
import { generateProductId, getCategoryStats } from '../../../utils/productIdGenerator.js';

/**
 * API для генерации уникального ID товара
 * GET /api/admin/generate-product-id?category=Брусчатка
 * GET /api/admin/generate-product-id?stats=true - получить статистику по всем категориям
 */
export async function GET({ request, url }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const categoryName = url.searchParams.get('category');
    const getStats = url.searchParams.get('stats') === 'true';
    
    if (getStats) {
      // Возвращаем статистику по всем категориям
      const stats = await getCategoryStats();
      
      return new Response(JSON.stringify({
        success: true,
        stats
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (!categoryName) {
      return new Response(JSON.stringify({ error: 'Не указана категория' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Генерируем ID товара
    const productId = await generateProductId(categoryName);
    
    if (!productId) {
      return new Response(JSON.stringify({ 
        error: 'Не удалось сгенерировать ID товара',
        details: `Категория "${categoryName}" не найдена или произошла ошибка`
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      productId,
      category: categoryName
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при генерации ID товара:', error);
    return new Response(JSON.stringify({ 
      error: 'Ошибка при генерации ID товара',
      details: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
