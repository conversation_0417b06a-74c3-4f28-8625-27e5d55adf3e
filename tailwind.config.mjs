/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: '#c8b499',
        'text-main': '#34322d',
        'dark-footer': '#2d2c28',
      },
      fontFamily: {
        sans: ['Titillium Web', 'sans-serif'],
        barlow: ['Barlow', 'sans-serif'],
      },
      container: {
        center: true,
        padding: '1rem',
      },
      boxShadow: {
        header: '0 2px 8px 0 rgba(0,0,0,0.04)',
      },
      transitionProperty: {
        'max-h': 'max-height',
      },
    },
  },
  plugins: [],
} 