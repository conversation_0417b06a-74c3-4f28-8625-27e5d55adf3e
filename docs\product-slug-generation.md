# Генерация SLUG для товаров

## Описание функциональности

Реализована полная система индивидуальных SEO-дружественных SLUG для каждого товара с автоматической транслитерацией кириллицы в латиницу. Все существующие товары получили SLUG, и система теперь использует только SLUG в каталоге для максимального SEO-эффекта.

## Что было добавлено

### 1. Обновление типа Product
- Добавлено обязательное поле `slug: string` в интерфейс Product
- Все товары теперь имеют уникальные SLUG
- Автоматическая генерация SLUG для всех существующих товаров

### 2. Поля SLUG в формах админ-панели

#### Страница создания товара (`/admin/products/new`)
- Добавлено поле "SLUG (URL товара)" после подкатегории
- Кнопка "Генерировать" для ручной генерации SLUG
- Автоматическая генерация при вводе названия товара
- Стилизация в соответствии с дизайном админ-панели

#### Страница редактирования товара (`/admin/products/edit/[id]`)
- Аналогичное поле SLUG с возможностью редактирования
- Отображение существующего SLUG (если есть)
- Кнопка генерации для обновления SLUG

### 3. Функция транслитерации
Создана функция `generateSlugFromName(name)` которая:
- Преобразует кириллические символы в латинские
- Приводит к нижнему регистру
- Заменяет пробелы на дефисы
- Удаляет недопустимые символы
- Очищает множественные дефисы
- Удаляет дефисы в начале и конце

### 4. Обновление URL и маршрутизации

#### Компоненты карточек товаров
- `ProductCardGrid.astro`: Обновлены ссылки для использования только SLUG
- `ProductCardList.astro`: Обновлены ссылки для использования только SLUG
- Убран fallback на ID - теперь используется только `product.slug`

#### Страница товара
- Обновлена логика поиска товара в `/products/[category]/[product].astro`
- Поддержка как SLUG, так и ID в URL
- Обновлены breadcrumbs для использования SLUG

## Примеры использования

### Примеры транслитерации
| Название товара | Сгенерированный SLUG |
|----------------|---------------------|
| Тротуарная плитка Классика | trotuarnaya-plitka-klassika |
| Брусчатка Старый город | bruschatka-staryj-gorod |
| Бордюр дорожный | bordyur-dorozhnyj |
| Водосток бетонный | vodostok-betonnyj |

### URL структура
```
Старый формат: /products/{categorySlug}/{productId}
Новый формат:  /products/{categorySlug}/{productSlug}

Примеры:
/products/trotuarnaya-plitka/TB-001                    (старый, работает)
/products/trotuarnaya-plitka/trotuarnaya-plitka-klassika (новый, SEO-дружественный)
```

## Совместимость

### Обратная совместимость
- ✅ Все существующие URL с ID продолжают работать (для старых закладок)
- ✅ Все товары теперь имеют SLUG и используют их в каталоге
- ✅ Полная миграция завершена: все товары имеют уникальные SLUG

### Логика поиска товаров
```javascript
// Поиск товара по SLUG или ID
product = productsData.find(p => {
  const matchesCategory = p.categorySlug === categorySlug;
  const matchesSlug = p.slug === productIdentifier;
  const matchesId = p.id === productIdentifier;

  return matchesCategory && (matchesSlug || matchesId);
});
```

## SEO преимущества

### До внедрения
```
/products/trotuarnaya-plitka/TB-001
/products/bruschatka/BR-001
/products/bordyury/BD-001
```

### После внедрения
```
/products/trotuarnaya-plitka/trotuarnaya-plitka-klassika
/products/bruschatka/bruschatka-staryj-gorod
/products/bordyury/bordyur-dorozhnyj-magistralnyj
```

### Преимущества для SEO
1. **Читаемые URL**: Пользователи и поисковики понимают содержание страницы по URL
2. **Ключевые слова в URL**: Улучшает релевантность для поисковых запросов
3. **Лучшая индексация**: Поисковые системы лучше понимают структуру сайта
4. **Улучшенный CTR**: Пользователи чаще кликают на понятные URL в поисковой выдаче

## Технические детали

### Маппинг кириллицы в латиницу
```javascript
const cyrillicToLatin = {
  'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
  'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
  'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
  'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
  // + строчные варианты
};
```

### Алгоритм очистки SLUG
1. Транслитерация кириллицы
2. Приведение к нижнему регистру
3. Замена пробелов на дефисы (`\s+` → `-`)
4. Удаление недопустимых символов (оставляем только `a-z0-9-`)
5. Замена множественных дефисов на одинарные (`--+` → `-`)
6. Удаление дефисов в начале и конце

## Использование в админ-панели

1. **Автоматическая генерация**: При создании товара SLUG генерируется автоматически при вводе названия
2. **Ручная генерация**: Кнопка "Генерировать" для создания SLUG из текущего названия
3. **Редактирование**: SLUG можно редактировать вручную в любое время
4. **Валидация**: Система автоматически очищает и нормализует введенные SLUG

## Миграция существующих товаров

Для добавления SLUG к существующим товарам:
1. Откройте страницу редактирования товара в админ-панели
2. Нажмите кнопку "Генерировать" рядом с полем SLUG
3. При необходимости отредактируйте сгенерированный SLUG
4. Сохраните изменения

Товар станет доступен по новому SEO-дружественному URL, при этом старый URL продолжит работать.
