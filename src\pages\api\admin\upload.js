import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';
import { generateImageFileName, getProductImageFiles } from '../../../utils/imageUtils.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const mediaDir = path.join(__dirname, '../../../../public/product');

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const productId = formData.get('productId');
    const productName = formData.get('productName') || productId;
    const isMain = formData.get('isMain') === 'true';

    if (!file || !productId) {
      return new Response(JSON.stringify({ error: 'Отсутствуют необходимые данные' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создаем директорию для товара, если она не существует
    const productDir = path.join(mediaDir, productId);
    try {
      await fs.mkdir(productDir, { recursive: true });
    } catch (err) {
      console.error('Ошибка при создании директории:', err);
    }

    // Определяем расширение файла
    const originalName = file.name || 'image.jpg';
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';

    // Определяем имя файла по новым правилам
    let fileName;
    if (isMain) {
      fileName = generateImageFileName(productId, productName, true, 1, extension);
    } else {
      // Для дополнительных изображений определяем следующий доступный индекс
      const existingFiles = await getProductImageFiles(productId);
      let nextIndex = 1;
      while (existingFiles.some(f => f.includes(`_${nextIndex}.`))) {
        nextIndex++;
      }
      fileName = generateImageFileName(productId, productName, false, nextIndex, extension);
    }

    const filePath = path.join(productDir, fileName);

    // Сохраняем файл
    const arrayBuffer = await file.arrayBuffer();
    const buffer = new Uint8Array(arrayBuffer);
    await fs.writeFile(filePath, buffer);

    // Формируем относительный путь для сохранения в JSON
    const relativePath = `${productId}/${fileName}`;

    return new Response(JSON.stringify({
      success: true,
      path: relativePath
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Ошибка загрузки файла:', error);
    return new Response(JSON.stringify({ error: 'Ошибка загрузки файла' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
