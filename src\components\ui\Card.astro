---
export interface Props {
  class?: string;
  variant?: 'default' | 'outline';
  style?: string;
}

const { class: className = '', variant = 'default', style } = Astro.props;

const baseClasses = 'rounded-lg border text-card-foreground';
const variantClasses = {
  default: 'bg-white shadow-sm border-gray-200',
  outline: 'border-gray-200'
};
---

<div class={`${baseClasses} ${variantClasses[variant]} ${className}`} style={style}>
  <slot />
</div>
