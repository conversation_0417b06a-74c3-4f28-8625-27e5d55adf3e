---
export interface Props {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'danger';
  showLabel?: boolean;
  class?: string;
}

const { 
  value, 
  max = 100, 
  size = 'md',
  variant = 'default',
  showLabel = false,
  class: className = '' 
} = Astro.props;

const percentage = Math.min((value / max) * 100, 100);

const sizeClasses = {
  sm: 'h-1',
  md: 'h-2',
  lg: 'h-3'
};

const variantClasses = {
  default: 'bg-blue-500',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  danger: 'bg-red-500'
};
---

<div class={`w-full ${className}`}>
  {showLabel && (
    <div class="flex justify-between text-sm text-gray-600 mb-1">
      <span>{value}</span>
      <span>{max}</span>
    </div>
  )}
  <div class={`w-full bg-gray-200 rounded-full ${sizeClasses[size]}`}>
    <div 
      class={`${sizeClasses[size]} ${variantClasses[variant]} rounded-full transition-all duration-300 ease-in-out`}
      style={`width: ${percentage}%`}
    ></div>
  </div>
  {showLabel && (
    <div class="text-xs text-gray-500 mt-1 text-center">
      {Math.round(percentage)}%
    </div>
  )}
</div>
