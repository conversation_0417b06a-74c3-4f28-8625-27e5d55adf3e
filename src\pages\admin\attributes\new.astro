---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных об атрибутах для выпадающего списка
import attributesFile from '../../../../data/product/attributes.json';
const attributesData = attributesFile;

// Определяем типы атрибутов и их отображаемые названия
const predefinedAttributeTypes = {
  colors: 'Цвет',
  textures: 'Текстура',
  strength_classes: 'Класс прочности',
  frost_resistance: 'Морозостойкость',
  water_absorption: 'Водопоглощение',
  standard_sizes: 'Размер',
  surfaces: 'Поверхность',
  patterns: 'Рисунок',
  color_pigments: 'Цветовые пигменты'
};

// Получаем все типы атрибутов из файла
const allAttributeTypes = {};
Object.keys(attributesData).forEach(key => {
  if (predefinedAttributeTypes[key]) {
    allAttributeTypes[key] = predefinedAttributeTypes[key];
  } else {
    // Для пользовательских типов создаем читаемое название
    allAttributeTypes[key] = key.replace(/_/g, ' ').replace(/^./, l => l.toUpperCase());
  }
});

// Получаем предварительно выбранный тип из URL параметров
const url = new URL(Astro.request.url);
const preselectedType = url.searchParams.get('type') || '';
---

<AdminLayout title="Добавить атрибут | LuxBeton">
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Добавить новый атрибут</h1>
      <a href="/admin/attributes" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">
        Назад к списку
      </a>
    </div>

    <form id="attribute-form" class="bg-white rounded-lg shadow-md p-6">
      <!-- Выбор типа атрибута -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold mb-4">Тип атрибута</h2>

        <div class="mb-4">
          <label for="attribute-type" class="block text-sm font-medium text-gray-700 mb-1">Выберите тип атрибута</label>
          <select
            id="attribute-type"
            name="attribute-type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            required
          >
            <option value="">Выберите тип атрибута</option>
            {Object.entries(allAttributeTypes).map(([key, name]) => (
              <option value={key}>{name}</option>
            ))}
          </select>
          <p class="text-xs text-gray-500 mt-1">
            Нужен новый тип атрибута?
            <a href="/admin/attributes/new-type" class="text-blue-600 hover:text-blue-800 underline">Создайте его здесь</a>
          </p>
        </div>


      </div>

      <!-- Динамические поля для атрибута -->
      <div id="attribute-fields" class="mb-6">
        <h2 class="text-xl font-semibold mb-4">Данные атрибута</h2>
        <div id="dynamic-fields">
          <p class="text-gray-500 text-sm">Выберите тип атрибута для отображения полей</p>
        </div>
      </div>

      <div class="flex justify-end space-x-2">
        <a href="/admin/attributes" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">
          Отмена
        </a>
        <button
          type="submit"
          class="create-attribute-btn text-white px-4 py-2 rounded"
          style="background-color: #3b82f6;"
        >
          Создать атрибут
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки создания атрибута */
  .create-attribute-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script define:vars={{ attributesData, preselectedType }}>
  // Элементы DOM
  const attributeTypeSelect = document.getElementById('attribute-type');
  const dynamicFields = document.getElementById('dynamic-fields');
  const attributeForm = document.getElementById('attribute-form');

  // Функция для получения существующих данных атрибута
  function getExistingAttributeData(type) {
    return attributesData[type] || null;
  }

  // Обработчик изменения типа атрибута
  attributeTypeSelect.addEventListener('change', function() {
    const selectedType = this.value;
    generateDynamicFields(selectedType);
  });

  // Генерация динамических полей формы
  function generateDynamicFields(type) {
    dynamicFields.innerHTML = '';

    if (!type) {
      dynamicFields.innerHTML = '<p class="text-gray-500 text-sm">Выберите тип атрибута для отображения полей</p>';
      return;
    }

    if (type === 'colors') {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="color-id" class="block text-sm font-medium text-gray-700 mb-1">ID</label>
          <input type="text" id="color-id" name="color-id"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Например: light_blue" required>
        </div>
        <div class="mb-4">
          <label for="color-name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
          <input type="text" id="color-name" name="color-name"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Например: Светло-голубой" required>
        </div>
        <div class="mb-4">
          <label for="color-hex" class="block text-sm font-medium text-gray-700 mb-1">Hex код</label>
          <input type="color" id="color-hex" name="color-hex" value="#000000"
                 class="w-full h-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
        </div>
      `;
    } else if (type === 'textures') {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="texture-name" class="block text-sm font-medium text-gray-700 mb-1">Название текстуры</label>
          <input type="text" id="texture-name" name="texture-name"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Например: матовая" required>
        </div>
      `;
    } else if (type === 'standard_sizes') {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="size-type" class="block text-sm font-medium text-gray-700 mb-1">Тип изделия</label>
          <select id="size-type" name="size-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
            <option value="">Выберите тип</option>
            <option value="pavers">Брусчатка (pavers)</option>
            <option value="curbs">Бордюры (curbs)</option>
            <option value="drainage">Дренаж (drainage)</option>
          </select>
        </div>
        <div class="mb-4">
          <label for="size-length" class="block text-sm font-medium text-gray-700 mb-1">Длина (мм)</label>
          <input type="number" id="size-length" name="size-length"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="200" required>
        </div>
        <div class="mb-4">
          <label for="size-width" class="block text-sm font-medium text-gray-700 mb-1">Ширина (мм)</label>
          <input type="number" id="size-width" name="size-width"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="100" required>
        </div>
        <div class="mb-4">
          <label for="size-height" class="block text-sm font-medium text-gray-700 mb-1">Высота (мм)</label>
          <input type="number" id="size-height" name="size-height"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="40" required>
        </div>
      `;
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(type)) {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="class-name" class="block text-sm font-medium text-gray-700 mb-1">Класс</label>
          <input type="text" id="class-name" name="class-name"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Например: B45" required>
        </div>
        <div class="mb-4">
          <label for="class-description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
          <textarea id="class-description" name="class-description" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Подробное описание класса" required></textarea>
        </div>
      `;
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(type)) {
      dynamicFields.innerHTML = `
        <div class="mb-4">
          <label for="item-id" class="block text-sm font-medium text-gray-700 mb-1">ID</label>
          <input type="text" id="item-id" name="item-id"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Например: textured_surface" required>
        </div>
        <div class="mb-4">
          <label for="item-name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
          <input type="text" id="item-name" name="item-name"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Например: Текстурированная поверхность" required>
        </div>
        <div class="mb-4">
          <label for="item-description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
          <textarea id="item-description" name="item-description" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Подробное описание" required></textarea>
        </div>
      `;
    } else {
      // Для пользовательских типов атрибутов определяем структуру на основе существующих данных
      const existingData = getExistingAttributeData(type);
      const isObjectStructure = existingData && existingData.length > 0 &&
                                typeof existingData[0] === 'object' && existingData[0] !== null;

      if (isObjectStructure) {
        // Показываем форму для объектной структуры
        dynamicFields.innerHTML = `
          <div class="mb-4">
            <label for="object-id" class="block text-sm font-medium text-gray-700 mb-1">ID (уникальный идентификатор)</label>
            <input type="text" id="object-id" name="object-id"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Например: osen" required>
          </div>
          <div class="mb-4">
            <label for="object-name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
            <input type="text" id="object-name" name="object-name"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Например: Осень" required>
          </div>
          <div class="mb-4">
            <label for="object-description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
            <textarea id="object-description" name="object-description" rows="3"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Подробное описание" required></textarea>
          </div>
        `;
      } else {
        // Показываем простую форму для строковых значений
        dynamicFields.innerHTML = `
          <div class="mb-4">
            <label for="simple-value" class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
            <input type="text" id="simple-value" name="simple-value"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Введите значение атрибута (например: Осень)" required>
          </div>
        `;
      }
    }
  }

  // Обработка отправки формы
  attributeForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    const type = attributeTypeSelect.value;
    if (!type) {
      await window.adminModal?.showError('Выберите тип атрибута');
      return;
    }

    let attributeData;

    // Собираем данные в зависимости от типа атрибута
    if (type === 'colors') {
      const id = document.getElementById('color-id').value.trim();
      const name = document.getElementById('color-name').value.trim();
      const hex = document.getElementById('color-hex').value;

      if (!id || !name) {
        await window.adminModal?.showError('Заполните все обязательные поля');
        return;
      }

      attributeData = { id, name, hex };
    } else if (type === 'textures') {
      const name = document.getElementById('texture-name').value.trim();
      if (!name) {
        await window.adminModal?.showError('Введите название текстуры');
        return;
      }
      attributeData = name;
    } else if (type === 'standard_sizes') {
      const sizeType = document.getElementById('size-type').value;
      const length = parseInt(document.getElementById('size-length').value);
      const width = parseInt(document.getElementById('size-width').value);
      const height = parseInt(document.getElementById('size-height').value);

      if (!sizeType || !length || !width || !height) {
        await window.adminModal?.showError('Заполните все поля размера');
        return;
      }

      attributeData = {
        sizeType: sizeType,
        sizeData: { length, width, height }
      };
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(type)) {
      const className = document.getElementById('class-name').value.trim();
      const description = document.getElementById('class-description').value.trim();

      if (!className || !description) {
        await window.adminModal?.showError('Заполните все поля');
        return;
      }

      attributeData = { class: className, description };
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(type)) {
      const id = document.getElementById('item-id').value.trim();
      const name = document.getElementById('item-name').value.trim();
      const description = document.getElementById('item-description').value.trim();

      if (!id || !name || !description) {
        await window.adminModal?.showError('Заполните все поля');
        return;
      }

      attributeData = { id, name, description };
    } else {
      // Обработка пользовательских типов атрибутов
      const existingData = getExistingAttributeData(type);
      const isObjectStructure = existingData && existingData.length > 0 &&
                                typeof existingData[0] === 'object' && existingData[0] !== null;

      if (isObjectStructure) {
        // Обрабатываем как объект
        const objectId = document.getElementById('object-id').value.trim();
        const objectName = document.getElementById('object-name').value.trim();
        const objectDescription = document.getElementById('object-description').value.trim();

        if (!objectId || !objectName) {
          await window.adminModal?.showError('Заполните обязательные поля (ID и название)');
          return;
        }

        attributeData = {
          id: objectId,
          name: objectName,
          description: objectDescription
        };
      } else {
        // Обрабатываем как простое значение
        const simpleValue = document.getElementById('simple-value');
        if (simpleValue) {
          const value = simpleValue.value.trim();
          if (!value) {
            await window.adminModal?.showError('Введите значение атрибута');
            return;
          }
          attributeData = value;
        } else {
          await window.adminModal?.showError('Не удалось найти поле для ввода данных');
          return;
        }
      }
    }

    try {
      const response = await fetch('/api/admin/attributes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ attributeType: type, attributeData })
      });

      if (response.ok) {
        await window.adminModal?.showSuccess('Атрибут успешно создан');
        // Перенаправляем на страницу атрибутов с текущей вкладкой
        window.location.href = `/admin/attributes?tab=${encodeURIComponent(type)}`;
      } else {
        const error = await response.json();
        await window.adminModal?.showError('Ошибка при создании: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      await window.adminModal?.showError('Ошибка при создании атрибута');
    }
  });

  // Инициализация: автоматически выбираем тип атрибута, если он передан в URL
  if (preselectedType && attributeTypeSelect) {
    // Проверяем, что такой тип существует в списке
    const option = attributeTypeSelect.querySelector(`option[value="${preselectedType}"]`);
    if (option) {
      attributeTypeSelect.value = preselectedType;
      // Генерируем поля для выбранного типа
      generateDynamicFields(preselectedType);
    }
  }
</script>
