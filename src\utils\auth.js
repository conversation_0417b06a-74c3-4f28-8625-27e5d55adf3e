import { createHash } from 'crypto';

// В реальном проекте следует использовать более безопасные методы хранения и проверки учетных данных
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD_HASH = '5f4dcc3b5aa765d61d8327deb882cf99'; // md5 хеш пароля 'password'

// Функция для проверки аутентификации
export function isAuthenticated(request) {
  try {
    let sessionValue = null;

    // Проверка куки сессии через Astro.cookies если доступно
    if (request.cookies) {
      sessionValue = request.cookies.get('admin_session')?.value;
    }

    // Fallback для проверки через заголовки
    if (!sessionValue) {
      const cookies = request.headers.get('cookie') || '';
      const sessionCookie = cookies.split(';').find(c => c.trim().startsWith('admin_session='));
      if (sessionCookie) {
        sessionValue = sessionCookie.split('=')[1];
      }
    }

    if (sessionValue) {
      const expectedToken = createSessionToken(ADMIN_USERNAME);
      return sessionValue === expectedToken;
    }

    return false;
  } catch (error) {
    console.error('Ошибка проверки аутентификации:', error);
    return false;
  }
}

// Функция для аутентификации пользователя
export function authenticateUser(username, password) {
  const passwordHash = createHash('md5').update(password).digest('hex');

  if (username === ADMIN_USERNAME && passwordHash === ADMIN_PASSWORD_HASH) {
    return createSessionToken(username);
  }

  return null;
}

// Создание токена сессии
function createSessionToken(username) {
  // В реальном проекте следует использовать более безопасный метод генерации токена
  // Используем статический токен для разработки
  return createHash('md5').update(`${username}-static-session-key`).digest('hex');
}

// Функция для выхода из системы
export function logout(cookies) {
  try {
    cookies.delete('admin_session', { path: '/' });
    return true;
  } catch (error) {
    console.error('Ошибка при выходе из системы:', error);
    return false;
  }
}
