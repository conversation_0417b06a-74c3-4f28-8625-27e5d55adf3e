import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const categoriesPath = path.join(__dirname, '../../../../data/product/categories.json');

export async function GET() {
  try {
    const data = await fs.readFile(categoriesPath, 'utf-8');
    const categoriesData = JSON.parse(data);
    
    // Фильтруем только видимые категории
    const visibleCategories = categoriesData.categories.filter(cat => cat.visibleInCatalog !== false);
    
    return new Response(JSON.stringify({ categories: visibleCategories }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения файла' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
