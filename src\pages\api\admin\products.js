import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const productsPath = path.join(__dirname, '../../../../data/product/products.json');
const productImagesDir = path.join(__dirname, '../../../../public/product');

export async function GET() {
  try {
    const data = await fs.readFile(productsPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения файла' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const data = await fs.readFile(productsPath, 'utf-8');
    const products = JSON.parse(data);

    // Создаем папку для изображений товара
    const productDir = path.join(productImagesDir, body.id);
    try {
      await fs.mkdir(productDir, { recursive: true });
      console.log(`Создана папка для товара: ${productDir}`);
    } catch (error) {
      console.warn(`Не удалось создать папку для товара ${body.id}:`, error.message);
    }

    // Добавление нового товара
    products.push(body);

    await fs.writeFile(productsPath, JSON.stringify(products, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      console.log('PUT request: Authentication failed');
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    console.log('PUT request: Updating product with ID:', body.id);
    console.log('PUT request: Product data:', JSON.stringify(body, null, 2));

    const data = await fs.readFile(productsPath, 'utf-8');
    let products = JSON.parse(data);

    // Найдем товар для обновления
    const productIndex = products.findIndex(product => product.id === body.id);
    if (productIndex === -1) {
      console.log('PUT request: Product not found with ID:', body.id);
      return new Response(JSON.stringify({ error: 'Товар не найден' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Обновление существующего товара
    products[productIndex] = body;
    console.log('PUT request: Product updated successfully');

    await fs.writeFile(productsPath, JSON.stringify(products, null, 2), 'utf-8');
    console.log('PUT request: File saved successfully');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('PUT request error:', error);
    return new Response(JSON.stringify({ error: 'Ошибка обновления данных: ' + error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    const data = await fs.readFile(productsPath, 'utf-8');
    let products = JSON.parse(data);

    // Удаление товара
    products = products.filter(product => product.id !== id);

    await fs.writeFile(productsPath, JSON.stringify(products, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка удаления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
