---
export interface Props {
  title: string;
  description?: string;
  icon?: string;
  actionText?: string;
  actionHref?: string;
  class?: string;
}

const { 
  title, 
  description, 
  icon, 
  actionText, 
  actionHref,
  class: className = '' 
} = Astro.props;

const defaultIcon = `
  <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
  </svg>
`;
---

<div class={`text-center py-12 ${className}`}>
  <div class="mx-auto flex h-12 w-12 items-center justify-center">
    <Fragment set:html={icon || defaultIcon} />
  </div>
  <h3 class="mt-4 text-lg font-medium text-gray-900">{title}</h3>
  {description && (
    <p class="mt-2 text-sm text-gray-500 max-w-sm mx-auto">{description}</p>
  )}
  {actionText && actionHref && (
    <div class="mt-6">
      <a 
        href={actionHref}
        class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors"
      >
        {actionText}
      </a>
    </div>
  )}
</div>
