---
import ClientReviewCard from './ClientReviewCard.astro';

const reviews = [
  { photo: '', name: '<PERSON><PERSON><PERSON><PERSON> Иван<PERSON>', review: 'Очень доволен качеством продукции! Всё быстро и профессионально.' },
  { photo: '', name: 'Мария Петрова', review: 'Отличный сервис и большой выбор. Рекомендую!' },
  { photo: '', name: 'Алексей Смирнов', review: 'Плитка и бордюры — на высшем уровне. Спасибо!' },
  { photo: '', name: 'Елена Кузнецова', review: 'Доставка вовремя, всё аккуратно. Буду заказывать ещё.' },
  { photo: '', name: '<PERSON>ерг<PERSON>й Волков', review: 'Приятно работать с профессионалами. Качество отличное.' },
  { photo: '', name: 'Ольга Сидорова', review: 'Цены адекватные, ассортимент большой. Всё понравилось.' },
  { photo: '', name: 'Дмитрий Орлов', review: 'Рекомендую всем, кто ищет надёжного поставщика.' },
  { photo: '', name: 'Анна Белова', review: 'Спасибо за индивидуальный подход и консультацию! Буду заказывать еще.' },
];
---

<div class="flex items-center gap-2 w-full">
  <button id="review-prev" aria-label="Назад" class="text-2xl px-2 py-1 disabled:opacity-30">←</button>
  <div id="review-slider" class="flex-1 overflow-x-auto scroll-smooth whitespace-nowrap no-scrollbar">
    {reviews.map((r, i) => (
      <div class="inline-block align-top w-full sm:w-1/2 lg:w-1/4 px-1 h-full">
        <div class="h-full min-h-[400px] flex flex-col justify-stretch">
          <ClientReviewCard {...r} />
        </div>
      </div>
    ))}
  </div>
  <button id="review-next" aria-label="Вперёд" class="text-2xl px-2 py-1 disabled:opacity-30">→</button>
</div>
<style>
  .no-scrollbar::-webkit-scrollbar { display: none; }
  .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
</style>
<script is:inline>
const slider = document.getElementById('review-slider');
const slides = slider.querySelectorAll('.inline-block');
const prev = document.getElementById('review-prev');
const next = document.getElementById('review-next');
let current = 0;
function getVisible() {
  if (window.innerWidth < 640) return 1;
  if (window.innerWidth < 1024) return 2;
  return 4;
}
function scrollToCurrent() {
  const slide = slides[current];
  prev.disabled = current === 0;
  next.disabled = current >= slides.length - getVisible();
}
prev.onclick = () => {
  if (current > 0) current--;
  prev.disabled = current === 0;
  next.disabled = current >= slides.length - getVisible();
};
next.onclick = () => {
  if (current < slides.length - getVisible()) current++;
  prev.disabled = current === 0;
  next.disabled = current >= slides.length - getVisible();
};
</script> 