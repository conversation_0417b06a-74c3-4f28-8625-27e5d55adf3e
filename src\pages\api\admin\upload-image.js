import { promises as fs } from 'fs';
import path from 'path';
import { isAuthenticated } from '../../../utils/auth';

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const productId = formData.get('productId');
    const imageType = formData.get('imageType'); // 'main' или 'additional'

    if (!file || !productId) {
      return new Response(JSON.stringify({ error: 'Файл и ID товара обязательны' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем тип файла
    if (!file.type.startsWith('image/')) {
      return new Response(JSON.stringify({ error: 'Файл должен быть изображением' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создаем папку для товара если её нет
    const productDir = path.join(process.cwd(), 'public', 'product', productId);
    try {
      await fs.access(productDir);
    } catch {
      await fs.mkdir(productDir, { recursive: true });
    }

    // Генерируем имя файла
    const fileExtension = path.extname(file.name);
    let fileName;
    
    if (imageType === 'main') {
      fileName = `${productId}_main${fileExtension}`;
    } else {
      // Для дополнительных изображений находим следующий доступный номер
      const existingFiles = await fs.readdir(productDir);
      const additionalFiles = existingFiles.filter(f => 
        f.startsWith(`${productId}_`) && 
        !f.includes('_main') && 
        (f.endsWith('.jpg') || f.endsWith('.jpeg') || f.endsWith('.png') || f.endsWith('.webp'))
      );
      
      const nextNumber = additionalFiles.length + 1;
      fileName = `${productId}_${nextNumber}${fileExtension}`;
    }

    // Сохраняем файл
    const filePath = path.join(productDir, fileName);
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    await fs.writeFile(filePath, buffer);

    // Возвращаем путь к файлу относительно папки product
    const relativePath = `${productId}/${fileName}`;

    return new Response(JSON.stringify({ 
      success: true, 
      imagePath: relativePath,
      fileName: fileName
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка загрузки изображения:', error);
    return new Response(JSON.stringify({ error: 'Ошибка загрузки файла: ' + error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
