---
interface Props {
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  size?: 'sm' | 'md' | 'lg';
  triggerClass?: string;
}

const {
  content,
  position = 'top',
  size = 'md',
  triggerClass = ''
} = Astro.props;

const sizeClasses = {
  sm: 'w-40 text-xs',
  md: 'w-48 text-sm',
  lg: 'w-56 text-base'
};

const iconSizeClasses = {
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-5 h-5'
};

const positionClasses = {
  top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
  bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
  left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
  right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
};

const arrowClasses = {
  top: 'absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-[#baa385]',
  bottom: 'absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-[#baa385]',
  left: 'absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-[#baa385]',
  right: 'absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-[#baa385]'
};
---

<span class={`relative inline-block tooltip-container ${triggerClass}`}>
  <svg
    class={`${iconSizeClasses[size]} text-gray-500 cursor-help hover:text-[#baa385] transition-colors duration-200 tooltip-trigger inline-block`}
    fill="currentColor"
    viewBox="0 0 20 20"
  >
    <path
      fill-rule="evenodd"
      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
      clip-rule="evenodd"
    />
  </svg>
  {content && (
    <div class={`tooltip absolute ${positionClasses[position]} ${sizeClasses[size]} bg-[#baa385] text-white border border-[#baa385] shadow-lg opacity-0 transition-opacity duration-200 pointer-events-none z-50`}>
      <div class="p-3">
        <p class="text-white leading-relaxed">
          {content}
        </p>
      </div>
      <div class={arrowClasses[position]}></div>
    </div>
  )}
</span>



<style>
  .tooltip-trigger:hover {
    color: #baa385 !important;
  }

  /* Ensure tooltip appears above other elements */
  .tooltip {
    z-index: 9999;
  }
</style>
