---
export interface Props {
  class?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string;
  height?: string;
  lines?: number;
}

const { 
  class: className = '',
  variant = 'rectangular',
  width,
  height,
  lines = 1
} = Astro.props;

const baseClasses = 'animate-pulse bg-gray-200';

const variantClasses = {
  text: 'h-4 rounded',
  circular: 'rounded-full',
  rectangular: 'rounded'
};

const style = [
  width && `width: ${width}`,
  height && `height: ${height}`
].filter(Boolean).join('; ');
---

{variant === 'text' && lines > 1 ? (
  <div class={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <div 
        class={`${baseClasses} ${variantClasses[variant]} ${index === lines - 1 ? 'w-3/4' : 'w-full'}`}
        style={style}
      ></div>
    ))}
  </div>
) : (
  <div 
    class={`${baseClasses} ${variantClasses[variant]} ${className}`}
    style={style}
  ></div>
)}
