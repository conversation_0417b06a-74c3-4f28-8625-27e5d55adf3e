<div class="flex flex-col w-full">
  <h2 class="text-white text-lg font-semibold mb-4 uppercase relative pb-2">
    Свяжитесь с нами
     <span class="absolute bottom-0 left-0 w-5 h-0.5 bg-primary"></span>
  </h2>
  <form id="contactForm" class="flex flex-col space-y-4">
    <input
      type="text"
      placeholder="Ваше имя"
      class="w-full p-3 bg-gray-200 text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary"
      name="name"
      required
    />
    <input
      type="text"
      id="phone"
      placeholder="Телефон"
      class="w-full p-3 bg-gray-200 text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary"
      name="phone"
      required
    />
    <textarea
      placeholder="Сообщение"
      rows="4"
      class="w-full p-3 bg-gray-200 text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary resize-none"
      name="message"
      required
    ></textarea>
    <button
      type="submit"
      id="submitButton"
      class="w-full bg-primary text-white py-3 px-6 uppercase font-bold transition-colors duration-300 hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
    >
      Отправить запрос
    </button>
  </form>
</div>

<script client:only="astro">
  const phoneInput = document.getElementById('phone');
  const submitButton = document.getElementById('submitButton');
  const contactForm = document.getElementById('contactForm');

  // Phone mask logic
  if (phoneInput instanceof HTMLInputElement) {
    phoneInput.addEventListener('input', (event) => {
      const target = event.target;
      if (target instanceof HTMLInputElement) {
        let input = target.value.replace(/\D/g, ''); // Remove non-digits
        let formattedInput = '';

        // Add +375 if not present
        if (!input.startsWith('375')) {
            if (input.startsWith('8')) {
                input = input.substring(1); // Remove leading 8
            } else if (input.startsWith('0')) {
                input = input.substring(1); // Remove leading 0
            }
            formattedInput = '+375' + input;
        } else {
            formattedInput = '+375' + input.substring(3);
        }

        // Basic formatting for digits after +375
        if (formattedInput.length > 4) {
           const digits = formattedInput.substring(4).replace(/\D/g, '');
           let currentFormat = '+375';
           if (digits.length > 0) currentFormat += ' (' + digits.substring(0, 2);
           if (digits.length > 2) currentFormat += ') ' + digits.substring(2, 5);
           if (digits.length > 5) currentFormat += '-' + digits.substring(5, 7);
           if (digits.length > 7) currentFormat += '-' + digits.substring(7, 9);
           formattedInput = currentFormat;
        } else if (formattedInput.length === 4) {
             // Keep just +375 if no other digits entered
             formattedInput = '+375';
        }

        target.value = formattedInput.substring(0, 19); // Limit length
      }
    });

     // Set initial value on focus if empty
     phoneInput.addEventListener('focus', () => {
         if (!phoneInput.value) {
             phoneInput.value = '+375';
         }
     });

      // Clear on blur if only mask remains
      phoneInput.addEventListener('blur', () => {
          if (phoneInput.value === '+375') {
              phoneInput.value = '';
          }
      });
  }


  // Button click logic (placeholder)
  if (contactForm instanceof HTMLFormElement && submitButton instanceof HTMLButtonElement) {
    contactForm.addEventListener('submit', (event) => {
      event.preventDefault(); // Prevent default form submission

      const nameInput = contactForm.querySelector('input[name="name"]');
      const messageInput = contactForm.querySelector('textarea[name="message"]');

      // Simulate sending data (replace with actual fetch/XHR)
      console.log('Form submitted!');
      console.log('Name:', nameInput instanceof HTMLInputElement ? nameInput.value : undefined);
      console.log('Phone:', phoneInput instanceof HTMLInputElement ? phoneInput.value : undefined);
      console.log('Message:', messageInput instanceof HTMLTextAreaElement ? messageInput.value : undefined);

      // Change button appearance
      submitButton.textContent = 'Спасибо, мы скоро с вами свяжемся';
      submitButton.classList.remove('bg-primary', 'hover:bg-primary-dark');
      submitButton.classList.add('bg-green-600', 'hover:bg-green-700'); // Use a green color
      submitButton.disabled = true; // Disable button after submission

      // You might want to add a success message or redirect the user
    });
  }
</script> 