---
interface Props {
  title: string;
  description?: string;
}

const { title, description = 'Aizen - Тротуарная плитка и изделия из бетона' } = Astro.props;
---

<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <title>{title}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      // @ts-ignore
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#baa385',
              'primary-dark': '#a89274',
            }
          }
        }
      }
    </script>

    <!-- Toolt<PERSON> -->
    <script src="/js/tooltip.js"></script>
  </head>
  <body class="bg-gray-50">
    <slot />
  </body>
</html>
