# Документация: Секция "Клиенты" (ClientsSection)

## Назначение
Секция для вывода доверия и отзывов клиентов. Состоит из блока статистики и слайдера отзывов.

## Структура
- Основной файл секции: `src/components/clients/ClientsSection.astro`
- Блок статистики: `src/components/clients/ClientStats.astro`
- Слайдер отзывов: `src/components/clients/ClientReviewSlider.tsx`
- Карточка отзыва: `src/components/clients/ClientReviewCard.astro`

## Как изменить статистику
- Откройте `ClientStats.astro`
- Измените цифры и подписи в соответствующих div
- Можно добавить/убрать блоки статистики по аналогии

## Как изменить отзывы
- Откройте `ClientReviewSlider.tsx`
- Массив `reviews` содержит объекты с полями:
  - `photo` — путь к фото клиента (или пусто для заглушки)
  - `name` — имя клиента
  - `review` — текст отзыва
- Для добавления нового отзыва добавьте новый объект в массив
- Для удаления — удалите нужный объект

## Как изменить фото клиента
- Загрузите фото в папку `public/images/clients/`
- В поле `photo` укажите путь, например: `/images/clients/ivan.jpg`
- Если поле пустое, будет отображаться заглушка "фото"

## Как изменить порядок отзывов
- Просто переставьте объекты в массиве `reviews` в нужном порядке

## Адаптивность
- На десктопе: 4 карточки в ряд
- На планшете: 2 карточки
- На мобильном: 1 карточка
- Стрелки навигации работают на всех устройствах

## Как работает слайдер
- Отображает только видимые карточки (4/2/1)
- Стрелки листают на количество видимых карточек вперёд/назад
- Автопрокрутки нет

## Доступность
- alt для фото клиента (имя)
- aria-label для стрелок
- Кнопки навигации доступны с клавиатуры

## Пример добавления отзыва
```js
{
  photo: '/images/clients/newclient.jpg',
  name: 'Новый Клиент',
  review: 'Очень доволен работой компании!'
}
```

## Важно
- Для сложных изменений структуры слайдера рекомендуется проконсультироваться с разработчиком 