/**
 * Утилиты для работы с изображениями товаров
 * Использует правила именования с суффиксами _main, _1, _2 и т.д.
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const publicDir = path.join(__dirname, '../../public');

/**
 * Автоматически определяет изображения товара в директории
 * @param {string} productId - ID товара
 * @returns {Promise<{main: string|null, additional: string[]}>} - Объект с путями к изображениям
 */
export async function autoDetectProductImages(productId) {
  const productDir = path.join(publicDir, 'product', productId);

  try {
    // Проверяем, существует ли директория товара
    await fs.access(productDir);
    const files = await fs.readdir(productDir);

    let mainImage = null;
    const additionalImages = [];

    // Ищем главное изображение
    const mainCandidates = files.filter(file =>
      file.toLowerCase().includes('_main.') &&
      (file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpeg'))
    );

    if (mainCandidates.length > 0) {
      mainImage = `${productId}/${mainCandidates[0]}`;
    }

    // Ищем дополнительные изображения
    for (let i = 1; i <= 10; i++) { // Поддерживаем до 10 дополнительных изображений
      const candidates = files.filter(file =>
        file.toLowerCase().includes(`_${i}.`) &&
        (file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpeg'))
      );

      if (candidates.length > 0) {
        additionalImages.push(`${productId}/${candidates[0]}`);
      }
    }

    return {
      main: mainImage,
      additional: additionalImages
    };

  } catch (error) {
    console.warn(`Не удалось найти изображения для товара ${productId}:`, error.message);
    return {
      main: null,
      additional: []
    };
  }
}

/**
 * Генерирует имя файла для изображения по новым правилам
 * @param {string} productId - ID товара
 * @param {string} productName - Название товара
 * @param {boolean} isMain - Является ли изображение главным
 * @param {number} index - Индекс дополнительного изображения (если не главное)
 * @param {string} extension - Расширение файла (jpg, png)
 * @returns {string} - Сгенерированное имя файла
 */
export function generateImageFileName(productId, productName, isMain = false, index = 1, extension = 'jpg') {
  // Создаем описательное название из названия товара
  const descriptiveName = productName
    .toLowerCase()
    .replace(/[^а-яё\w\s]/gi, '') // Убираем специальные символы, оставляем буквы и пробелы
    .replace(/\s+/g, '-') // Заменяем пробелы на дефисы
    .substring(0, 30); // Ограничиваем длину

  const suffix = isMain ? '_main' : `_${index}`;
  return `${descriptiveName}${suffix}.${extension}`;
}

/**
 * Проверяет, существует ли изображение товара
 * @param {string} imagePath - Относительный путь к изображению (например, "TB-001/main.jpg")
 * @returns {Promise<boolean>} - true, если изображение существует
 */
export async function imageExists(imagePath) {
  try {
    const fullPath = path.join(publicDir, 'product', imagePath);
    await fs.access(fullPath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Получает список всех изображений в директории товара
 * @param {string} productId - ID товара
 * @returns {Promise<string[]>} - Массив имен файлов изображений
 */
export async function getProductImageFiles(productId) {
  const productDir = path.join(publicDir, 'product', productId);

  try {
    const files = await fs.readdir(productDir);
    return files.filter(file =>
      file.toLowerCase().endsWith('.jpg') ||
      file.toLowerCase().endsWith('.png') ||
      file.toLowerCase().endsWith('.jpeg')
    );
  } catch (error) {
    console.warn(`Не удалось получить список изображений для товара ${productId}:`, error.message);
    return [];
  }
}
