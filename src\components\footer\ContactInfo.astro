---
import SocialLinks from '../utils/SocialLinks.astro';

interface Props {
  email?: string;
  phone?: string;
  address?: string;
}

const { 
  email = "<EMAIL>",
  phone = "******-558-2995",
  address = "525 7th Avenue - Suite 1601, New York, NY 10001",
} = Astro.props;
---

<div class="flex flex-col">
  <h2 class="text-white text-lg font-semibold mb-4 uppercase relative pb-2">
    Контактная информация
    <span class="absolute bottom-0 left-0 w-5 h-0.5 bg-primary"></span>
  </h2>
  <ul class="text-text-light text-base font-barlow leading-loose mb-6">
    <li>Email: {email}</li>
    <li>Телефон: {phone}</li>
    <li>Адрес: {address}</li>
    <li class="flex items-center gap-2"><span class="iconify text-lg" data-icon="mdi:calendar-clock" />Время работы: Mon – Fri 9am – 6pm</li>
  </ul>
  <h3 class="text-white text-lg font-semibold mb-4 uppercase relative pb-2">
    Соц. сети
     <span class="absolute bottom-0 left-0 w-5 h-0.5 bg-primary"></span>
  </h3>
  <SocialLinks />
</div> 