# Генерация SLUG для категорий

## Описание функциональности

Добавлена возможность автоматической генерации человеко-читаемых SLUG для категорий с транслитерацией кириллицы в латиницу, аналогично функциональности создания товаров.

## Что было добавлено

### 1. Кнопка генерации SLUG
- Добавлена кнопка "Генерировать" рядом с полем SLUG в модальном окне создания/редактирования категории
- Кнопка имеет tooltip с описанием функциональности
- Стилизована в соответствии с дизайном админ-панели (синий цвет #3b82f6)

### 2. Функция транслитерации
Создана функция `generateSlugFromName(name)` которая:
- Преобразует кириллические символы в латинские
- Приводит к нижнему регистру
- Заменяет пробелы на дефисы
- Удаляет недопустимые символы
- Очищает множественные дефисы
- Удаляет дефисы в начале и конце

### 3. Автоматическая генерация
- SLUG автоматически генерируется при вводе названия категории (только в режиме создания)
- Ручная генерация доступна через кнопку в любое время

## Примеры транслитерации

| Название категории | Сгенерированный SLUG |
|-------------------|---------------------|
| Тротуарная плитка | trotuarnaya-plitka |
| Брусчатка | bruschatka |
| Малые архитектурные формы | malye-arhitekturnye-formy |
| Водостоки | vodostoki |
| Памятники | pamyatniki |

## Технические детали

### Маппинг кириллицы в латиницу
```javascript
const cyrillicToLatin = {
  'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
  'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
  'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
  'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
  // + строчные варианты
};
```

### Алгоритм очистки SLUG
1. Транслитерация кириллицы
2. Приведение к нижнему регистру
3. Замена пробелов на дефисы (`\s+` → `-`)
4. Удаление недопустимых символов (оставляем только `a-z0-9-`)
5. Замена множественных дефисов на одинарные (`--+` → `-`)
6. Удаление дефисов в начале и конце

## Использование

1. **Автоматическая генерация**: При создании новой категории SLUG генерируется автоматически при вводе названия
2. **Ручная генерация**: Нажмите кнопку "Генерировать" для создания SLUG из текущего названия
3. **Редактирование**: SLUG можно редактировать вручную в любое время

## Совместимость

Функциональность полностью совместима с существующей системой:
- Сохраняет все существующие SLUG
- Работает с API категорий
- Интегрирована с системой валидации форм
- Поддерживает режимы создания и редактирования категорий
