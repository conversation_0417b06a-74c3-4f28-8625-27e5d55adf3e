---
export interface Props {
  icon: string; // путь к png
  title: string;
  description: string;
}
const { icon, title, description } = Astro.props;
---
<div class="bg-[#f5f3ef] shadow-xl p-8 md:p-10 min-h-[260px] flex flex-col justify-between transition-transform hover:-translate-y-1">
  <div>
    <div class="mb-6 flex justify-center">
      <img src={icon} alt="" aria-hidden="true" class="w-16 h-16 object-contain" loading="lazy" />
    </div>
    <h3 class="text-lg font-bold uppercase text-black mb-4 tracking-wide">{title}</h3>
    <p class="text-gray-700 mb-8">{description}</p>
  </div>
</div> 