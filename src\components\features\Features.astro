---
import FeatureCard from './FeatureCard.astro';
---

<section class="py-20 bg-[#e98135]">
  <div class="container mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-white uppercase mb-2 tracking-wide">Характеристики продукции</h2>
      <div class="text-white text-lg font-medium mb-4 uppercase tracking-wider">Высокое качество и надежность нашей продукции обеспечивается использованием современных технологий и материалов</div>
      <div class="mx-auto w-20 h-1 bg-white opacity-60 mb-2"></div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
      <FeatureCard
        icon="/images/featurecard/rain-cloud.png"
        title="Устойчивость к солнцу и погоде"
        description="Имеет высокую устойчивость к воздействию солнечных лучей и других природных явлений"
      />
      <FeatureCard
        icon="/images/featurecard/water-100.png"
        title="Низкое водопоглощение"
        description="Практически нулевое водопоглощение"
      />
      <FeatureCard
        icon="/images/featurecard/rain-cloud.png"
        title="Устойчивость к истираемости"
        description="Повышенная устойчивость к истираемости"
      />
      <FeatureCard
        icon="/images/featurecard/rain-cloud.png"
        title="Газонепроницаемость"
        description="Газонепроницаемость"
      />
      <FeatureCard
        icon="/images/featurecard/rain-cloud.png"
        title="Морозостойкость"
        description="Высокая морозостойкость"
      />
      <FeatureCard
        icon="/images/featurecard/rain-cloud.png"
        title="Сопротивление хлоридам"
        description="Сопротивляемость проникновению хлоридов"
      />
    </div>
  </div>
</section> 