document.addEventListener('DOMContentLoaded', function () {
  document.querySelectorAll('.custom-select-container').forEach(function (container) {
    if (container.dataset.disabled === 'true') {
      container.classList.add('disabled');
      return;
    }
    const options = JSON.parse(container.dataset.options || '[]');
    const dropdown = container.querySelector('.custom-select-dropdown');
    const selectedDiv = container.querySelector('.custom-select-selected');
    const valueSpan = container.querySelector('.custom-select-value');
    const input = container.querySelector('input[type="hidden"]');
    let value = container.dataset.value || '';
    let placeholder = container.dataset.placeholder || 'Выберите...';
    let isOpen = false;

    function renderOptions() {
      dropdown.innerHTML = '';
      options.forEach(function (opt) {
        const li = document.createElement('li');
        li.className = 'custom-select-option' + (opt.value === value ? ' selected' : '');
        li.setAttribute('role', 'option');
        li.setAttribute('tabindex', '0');
        li.textContent = opt.label;
        li.addEventListener('click', function (e) {
          value = opt.value;
          input.value = value;
          valueSpan.textContent = opt.label;
          dropdown.style.display = 'none';
          isOpen = false;
          renderOptions();
          container.dispatchEvent(new CustomEvent('change', { detail: value }));
        });
        dropdown.appendChild(li);
      });
    }

    function openDropdown() {
      if (isOpen) return;
      dropdown.style.display = 'block';
      selectedDiv.classList.add('open');
      isOpen = true;
    }
    function closeDropdown() {
      dropdown.style.display = 'none';
      selectedDiv.classList.remove('open');
      isOpen = false;
    }
    selectedDiv.addEventListener('click', function (e) {
      if (isOpen) {
        closeDropdown();
      } else {
        openDropdown();
      }
    });
    document.addEventListener('click', function (e) {
      if (!container.contains(e.target)) {
        closeDropdown();
      }
    });
    // Инициализация значения
    const selectedOpt = options.find(function (opt) { return opt.value === value; });
    valueSpan.textContent = selectedOpt ? selectedOpt.label : placeholder;
    input.value = value;
    renderOptions();
  });
}); 