---
interface Props {
  id?: string;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonClass?: string;
  cancelButtonClass?: string;
}

const {
  id = 'confirm-modal',
  title = 'Подтверждение действия',
  message = 'Вы уверены, что хотите выполнить это действие?',
  confirmText = 'Подтвердить',
  cancelText = 'Отмена',
  confirmButtonClass = 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300',
  cancelButtonClass = 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
} = Astro.props;
---

<!-- Backdrop -->
<div
  id={id}
  class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 opacity-0 invisible transition-all duration-300 ease-out"
  data-modal="confirm"
>
  <!-- Modal Container -->
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full transform scale-95 transition-all duration-300 ease-out">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900" data-modal-title>
        {title}
      </h3>
    </div>

    <!-- Body -->
    <div class="px-6 py-4">
      <p class="text-gray-600 leading-relaxed" data-modal-message>
        {message}
      </p>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-200 flex flex-col-reverse sm:flex-row sm:justify-end gap-3">
      <!-- Cancel Button -->
      <button
        type="button"
        class={`px-4 py-2 rounded-md font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${cancelButtonClass}`}
        data-modal-cancel
      >
        {cancelText}
      </button>

      <!-- Confirm Button -->
      <button
        type="button"
        class={`px-4 py-2 rounded-md font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${confirmButtonClass}`}
        data-modal-confirm
      >
        {confirmText}
      </button>
    </div>
  </div>
</div>

<script>
  class ConfirmModal {
    constructor(modalId) {
      this.modal = document.getElementById(modalId);
      this.titleElement = this.modal.querySelector('[data-modal-title]');
      this.messageElement = this.modal.querySelector('[data-modal-message]');
      this.confirmButton = this.modal.querySelector('[data-modal-confirm]');
      this.cancelButton = this.modal.querySelector('[data-modal-cancel]');
      this.backdrop = this.modal;

      this.resolvePromise = null;
      this.rejectPromise = null;

      this.init();
    }

    init() {
      // Close on backdrop click
      this.backdrop.addEventListener('click', (e) => {
        if (e.target === this.backdrop) {
          this.close(false);
        }
      });

      // Close on cancel button
      this.cancelButton.addEventListener('click', () => {
        this.close(false);
      });

      // Confirm on confirm button
      this.confirmButton.addEventListener('click', () => {
        this.close(true);
      });

      // Close on Escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isOpen()) {
          this.close(false);
        }
      });
    }

    show(options = {}) {
      return new Promise((resolve, reject) => {
        this.resolvePromise = resolve;
        this.rejectPromise = reject;

        // Update content if provided
        if (options.title) {
          this.titleElement.textContent = options.title;
        }
        if (options.message) {
          this.messageElement.textContent = options.message;
        }
        if (options.confirmText) {
          this.confirmButton.textContent = options.confirmText;
        }
        if (options.cancelText) {
          this.cancelButton.textContent = options.cancelText;
        }

        // Show modal
        this.modal.classList.remove('opacity-0', 'invisible');
        this.modal.classList.add('opacity-100', 'visible');

        // Animate modal content
        const modalContent = this.modal.querySelector('div > div');
        modalContent.classList.remove('scale-95');
        modalContent.classList.add('scale-100');

        // Focus confirm button
        setTimeout(() => {
          this.confirmButton.focus();
        }, 100);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
      });
    }

    close(confirmed) {
      // Hide modal
      this.modal.classList.remove('opacity-100', 'visible');
      this.modal.classList.add('opacity-0', 'invisible');

      // Animate modal content
      const modalContent = this.modal.querySelector('div > div');
      modalContent.classList.remove('scale-100');
      modalContent.classList.add('scale-95');

      // Restore body scroll
      document.body.style.overflow = '';

      // Resolve promise
      if (this.resolvePromise) {
        this.resolvePromise(confirmed);
        this.resolvePromise = null;
        this.rejectPromise = null;
      }
    }

    isOpen() {
      return this.modal.classList.contains('visible');
    }
  }

  // Initialize modal when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('confirm-modal')) {
      window.confirmModal = new ConfirmModal('confirm-modal');
    }
  });
</script>

<style>
  /* Ensure modal appears above everything */
  [data-modal="confirm"] {
    z-index: 9999;
  }

  /* Smooth transitions */
  [data-modal="confirm"] > div {
    transition: transform 0.3s ease-out;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    [data-modal="confirm"] > div {
      margin: 1rem;
      max-width: calc(100vw - 2rem);
    }

    [data-modal="confirm"] .px-6 {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
</style>
